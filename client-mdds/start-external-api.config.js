require("dotenv").config(); // Load file .env
const fs = require("fs");

// Lấy file JAR mới nhất trong thư mục target
const jarFile = fs
  .readdirSync("./target")
  .filter((file) => file.endsWith(".jar"))
  .sort((a, b) => fs.statSync(`./target/${b}`).mtime - fs.statSync(`./target/${a}`).mtime)[0];

// Kiểm tra nếu không tìm thấy file JAR nào thì báo lỗi và dừng lại
if (!jarFile) {
  console.error("❗ Không tìm thấy file JAR trong thư mục ./target");
  process.exit(1);
}

console.log(`🚀 Đã tìm thấy file JAR mới nhất: ./target/${jarFile}`);

module.exports = {
  apps: [
    {
      name: "api-gateway",
      script: "java",
      args: `-jar ./target/${jarFile}`, // Chạy JAR mới nhất
      instances: 1,
      exec_interpreter: "none", // Bắt buộc PM2 không hiểu nhầm là file Node.js
      exec_mode: "fork",
      autorestart: true,
      watch: false,
      max_memory_restart: "1G",
      env: process.env,

      // Ghi log riêng ra file
      output: "logs/api-gateway.out.log", // Log output thông thường
      error: "logs/api-gateway.error.log", // Log lỗi riêng
      log_date_format: "YYYY-MM-DD HH:mm:ss",
    },
  ],
};
