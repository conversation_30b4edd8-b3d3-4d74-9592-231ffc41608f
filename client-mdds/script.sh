#!/bin/bash

# Hiển thị menu lựa chọn
echo "🔧 Chọn kiểu tăng phiên bản:"
echo "1. <PERSON><PERSON><PERSON> Major (1.x.x → 2.0.0)"
echo "2. Tăng Minor (x.1.x → x.2.0)"
echo "3. Tăng Patch (x.x.1 → x.x.2)"
read -p "👉 Nhập lựa chọn (1/2/3): " version_type

# Đọc phiên bản hiện tại từ pom.xml
CURRENT_VERSION=$(mvn help:evaluate -Dexpression=project.version -q -DforceStdout)

# Tách major.minor.patch
IFS='.' read -r major minor patch <<< "${CURRENT_VERSION%-SNAPSHOT}"

# Xử lý lựa chọn
case $version_type in 
    1)
        ((major++))
        minor=0
        patch=0
        ;;
    2)
        ((minor++))
        patch=0
        ;;
    3)
        ((patch++))
        ;;
    *)
        echo "❗ Lựa chọn không hợp lệ!"
        exit 1
        ;;
esac

# Tạo phiên bản mới
NEW_VERSION="${major}.${minor}.${patch}-SNAPSHOT"
echo "🚀 Phiên bản mới: $NEW_VERSION"

# Cập nhật phiên bản trong pom.xml
mvn versions:set -DnewVersion=$NEW_VERSION

# # Build lại dự án
# mvn clean package

# Tìm file JAR mới build                                  
JAR_FILE=$(find target -name "*.jar" | head -n 1)

# # Kiểm tra file JAR có tồn tại không
# if [ -z "$JAR_FILE" ]; then
#     echo "❗ Không tìm thấy file JAR!"
#     exit 1
# fi

# echo "✅ Build thành công: $JAR_FILE"
# java -jar "$JAR_FILE"

# # Lưu phiên bản mới vào Git
# git add pom.xml
# git commit -m "🚀 Build version mới: $NEW_VERSION"
# git push origin main


echo "============================"
echo "🚀 Java Build & Run Script 🚀"
echo "============================"
echo "1. Build project"
echo "2. Run JAR file"
echo "3. Clean project"
echo "4. Exit"
echo "============================"

# Lựa chọn thao tác
read -p "👉 Nhập lựa chọn (1/2/3/4): " choice

case $choice in
    1)
        echo "🚧 Đang build project..."
        mvn clean package
        if [ $? -ne 0 ]; then
            echo "❌ Build thất bại!"
            exit 1
        fi
        echo "✅ Build thành công!"
        ;;
    2)
        echo "🚀 Đang chạy ứng dụng..."
        java -jar "$JAR_FILE"
        ;;
    3)
        echo "🧹 Đang dọn dẹp project..."
        mvn clean
        echo "✅ Đã dọn dẹp!"
        ;;
    4)
        echo "👋 Tạm biệt!"
        exit 0
        ;;
    *)
        echo "❗ Lựa chọn không hợp lệ!"
        ;;
esac