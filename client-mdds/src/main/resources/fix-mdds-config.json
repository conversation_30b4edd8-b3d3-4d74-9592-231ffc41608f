{"ipListen": "*************", "joinGroupMessage": [{"group": "GSTO000001", "messageName": "(Unicast) Manual Recovery - Batch", "ip": "***********", "port": 55101, "market": "STO"}, {"group": "GSTO000002", "messageName": "(Unicast) Manual Recovery - price", "ip": "***********", "port": 55102, "market": "STO"}, {"group": "GSTO000003", "messageName": "Time Stamp (Polling)", "ip": "**********", "port": 55111, "market": "STO"}, {"group": "GSTO000004", "messageName": "Security Definition", "ip": "**********", "port": 55112, "market": "STO"}, {"group": "GSTO000005", "messageName": "Market Maker Information", "ip": "**********", "port": 55113, "market": "STO"}, {"group": "GSTO000006", "messageName": "Symbol Event", "ip": "**********", "port": 55114, "market": "STO"}, {"group": "GSTO000007", "messageName": "Index Constituents Information", "ip": "**********", "port": 55115, "market": "STO"}, {"group": "GSTO000008", "messageName": "Foreigner Order Limit", "ip": "**********", "port": 55116, "market": "STO"}, {"group": "GSTO000009", "messageName": "Market Data(1st N level best orders)", "ip": "**********", "port": 55121, "market": "STO"}, {"group": "GSTO000010", "messageName": "Price Recovery for Market Data(1st N level best orders)", "ip": "**********", "port": 55122, "market": "STO"}, {"group": "GSTO000011", "messageName": "Market Data(2nd N level best orders)", "ip": "**********", "port": 55123, "market": "STO"}, {"group": "GSTO000012", "messageName": "Price Recovery for Market Data(2nd N level best orders)", "ip": "**********", "port": 55124, "market": "STO"}, {"group": "GSTO000013", "messageName": "Market Data(3th N level best orders)", "ip": "**********", "port": 55125, "market": "STO"}, {"group": "GSTO000014", "messageName": "Price Recovery for Market Data(3th N level best orders)", "ip": "**********", "port": 55126, "market": "STO"}, {"group": "GSTO000015", "messageName": "Trading Result of Foreign Investors", "ip": "**********", "port": 55131, "market": "STO"}, {"group": "GSTO000016", "messageName": "Investor per Symbol", "ip": "**********", "port": 55132, "market": "STO"}, {"group": "GSTO000017", "messageName": "Investor per Industry", "ip": "**********", "port": 55133, "market": "STO"}, {"group": "GSTO000018", "messageName": "Top N Members per Symbol", "ip": "**********", "port": 55134, "market": "STO"}, {"group": "GSTO000019", "messageName": "Top N Symbols", "ip": "**********", "port": 55135, "market": "STO"}, {"group": "GSTO000020", "messageName": "Index Group 1", "ip": "**********", "port": 55141, "market": "STO"}, {"group": "GSTO000021", "messageName": "Index Group 2", "ip": "**********", "port": 55142, "market": "STO"}, {"group": "GSTO000022", "messageName": "Index Group 3", "ip": "**********", "port": 55143, "market": "STO"}, {"group": "GSTO000023", "messageName": "Index Group 4", "ip": "**********", "port": 55144, "market": "STO"}, {"group": "GSTO000024", "messageName": "Index Group 5", "ip": "**********", "port": 55145, "market": "STO"}, {"group": "GSTO000025", "messageName": "Index Group 6", "ip": "**********", "port": 55146, "market": "STO"}, {"group": "GSTO000026", "messageName": "Index Group 7", "ip": "**********", "port": 55147, "market": "STO"}, {"group": "GSTO000027", "messageName": "Index Group 8", "ip": "**********", "port": 55148, "market": "STO"}, {"group": "GSTO000028", "messageName": "Index Group 9", "ip": "**********", "port": 55149, "market": "STO"}, {"group": "GSTO000029", "messageName": "Index Group 10", "ip": "**********", "port": 55150, "market": "STO"}, {"group": "GSTO000030", "messageName": "ETF iNav", "ip": "**********", "port": 55151, "market": "STO"}, {"group": "GSTO000031", "messageName": "ETF iIndex", "ip": "**********", "port": 55152, "market": "STO"}, {"group": "GSTO000032", "messageName": "ETF TrackingError", "ip": "**********", "port": 55153, "market": "STO"}, {"group": "GSTO000033", "messageName": "Disclosure", "ip": "**********", "port": 55161, "market": "STO"}]}