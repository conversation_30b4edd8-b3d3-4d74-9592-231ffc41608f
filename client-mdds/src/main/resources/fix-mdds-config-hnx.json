{"ipListen": "*************", "joinGroupMessage": [{"group": "GSTX000001", "messageName": "(Unicast) Manual Recovery - Batch", "ip": "***********", "port": 55601, "market": "STX"}, {"group": "GSTX000002", "messageName": "(Unicast) Manual Recovery - price", "ip": "***********", "port": 55602, "market": "STX"}, {"group": "GSTX000003", "messageName": "Time Stamp (Polling)", "ip": "**********", "port": 55611, "market": "STX"}, {"group": "GSTX000004", "messageName": "Security Definition", "ip": "**********", "port": 55612, "market": "STX"}, {"group": "GSTX000005", "messageName": "Market Maker Information", "ip": "**********", "port": 55613, "market": "STX"}, {"group": "GSTX000006", "messageName": "Symbol Event", "ip": "**********", "port": 55614, "market": "STX"}, {"group": "GSTX000007", "messageName": "Index Constituents Information", "ip": "**********", "port": 55615, "market": "STX"}, {"group": "GSTX000008", "messageName": "Foreigner Order Limit", "ip": "**********", "port": 55616, "market": "STX"}, {"group": "GSTX000009", "messageName": "Market Data(1st N level best orders)", "ip": "**********", "port": 55621, "market": "STX"}, {"group": "GSTX000010", "messageName": "Price Recovery for Market Data(1st N level best orders)", "ip": "**********", "port": 55622, "market": "STX"}, {"group": "GSTX000011", "messageName": "Market Data(2nd N level best orders)", "ip": "**********", "port": 55623, "market": "STX"}, {"group": "GSTX000012", "messageName": "Price Recovery for Market Data(2nd N level best orders)", "ip": "**********", "port": 55624, "market": "STX"}, {"group": "GSTX000013", "messageName": "Market Data(3th N level best orders)", "ip": "**********", "port": 55625, "market": "STX"}, {"group": "GSTX000014", "messageName": "Price Recovery for Market Data(3th N level best orders)", "ip": "**********", "port": 55626, "market": "STX"}, {"group": "GSTX000015", "messageName": "Trading Result of Foreign Investors", "ip": "**********", "port": 55631, "market": "STX"}, {"group": "GSTX000016", "messageName": "Investor per Symbol", "ip": "**********", "port": 55632, "market": "STX"}, {"group": "GSTX000017", "messageName": "Investor per Industry", "ip": "**********", "port": 55633, "market": "STX"}, {"group": "GSTX000018", "messageName": "Top N Members per Symbol", "ip": "**********", "port": 55634, "market": "STX"}, {"group": "GSTX000019", "messageName": "Top N Symbols", "ip": "**********", "port": 55635, "market": "STX"}, {"group": "GSTX000020", "messageName": "Index Group 1", "ip": "**********", "port": 55641, "market": "STX"}, {"group": "GSTX000021", "messageName": "Index Group 2", "ip": "**********", "port": 55642, "market": "STX"}, {"group": "GSTX000022", "messageName": "Index Group 3", "ip": "**********", "port": 55643, "market": "STX"}, {"group": "GSTX000023", "messageName": "Index Group 4", "ip": "**********", "port": 55644, "market": "STX"}, {"group": "GSTX000024", "messageName": "Index Group 5", "ip": "**********", "port": 55645, "market": "STX"}, {"group": "GSTX000025", "messageName": "ETF iNav", "ip": "**********", "port": 55651, "market": "STX"}, {"group": "GSTX000026", "messageName": "ETF iIndex", "ip": "**********", "port": 55652, "market": "STX"}, {"group": "GSTX000027", "messageName": "ETF TrackingError", "ip": "**********", "port": 55653, "market": "STX"}, {"group": "GUPX000001", "messageName": "(Unicast) Manual Recovery - Batch", "ip": "***********", "port": 55701, "market": "UPX"}, {"group": "GUPX000002", "messageName": "(Unicast) Manual Recovery - price", "ip": "***********", "port": 55702, "market": "UPX"}, {"group": "GUPX000003", "messageName": "Time Stamp (Polling)", "ip": "**********", "port": 55711, "market": "UPX"}, {"group": "GUPX000004", "messageName": "Security Definition", "ip": "**********", "port": 55712, "market": "UPX"}, {"group": "GUPX000005", "messageName": "Market Maker Information", "ip": "**********", "port": 55713, "market": "UPX"}, {"group": "GUPX000006", "messageName": "Symbol Event", "ip": "**********", "port": 55714, "market": "UPX"}, {"group": "GUPX000007", "messageName": "Index Constituents Information", "ip": "**********", "port": 55715, "market": "UPX"}, {"group": "GUPX000008", "messageName": "Foreigner Order Limit", "ip": "**********", "port": 55716, "market": "UPX"}, {"group": "GUPX000009", "messageName": "Market Data(1st N level best orders)", "ip": "**********", "port": 55721, "market": "UPX"}, {"group": "GUPX000010", "messageName": "Price Recovery for Market Data(1st N level best orders)", "ip": "**********", "port": 55722, "market": "UPX"}, {"group": "GUPX000011", "messageName": "Market Data(2nd N level best orders)", "ip": "**********", "port": 55723, "market": "UPX"}, {"group": "GUPX000012", "messageName": "Price Recovery for Market Data(2nd N level best orders)", "ip": "**********", "port": 55724, "market": "UPX"}, {"group": "GUPX000013", "messageName": "Market Data(3th N level best orders)", "ip": "**********", "port": 55725, "market": "UPX"}, {"group": "GUPX000014", "messageName": "Price Recovery for Market Data(3th N level best orders)", "ip": "**********", "port": 55726, "market": "UPX"}, {"group": "GUPX000015", "messageName": "Trading Result of Foreign Investors", "ip": "**********", "port": 55731, "market": "UPX"}, {"group": "GUPX000016", "messageName": "Investor per Symbol", "ip": "**********", "port": 55732, "market": "UPX"}, {"group": "GUPX000017", "messageName": "Investor per Industry", "ip": "**********", "port": 55733, "market": "UPX"}, {"group": "GUPX000018", "messageName": "Top N Members per Symbol", "ip": "**********", "port": 55734, "market": "UPX"}, {"group": "GUPX000019", "messageName": "Top N Symbols", "ip": "**********", "port": 55735, "market": "UPX"}, {"group": "GUPX000020", "messageName": "UPX Index Group 6", "ip": "**********", "port": 55741, "market": "UPX"}, {"group": "GUPX000021", "messageName": "UPX Index Group 7", "ip": "**********", "port": 55742, "market": "UPX"}, {"group": "GUPX000022", "messageName": "UPX Index Group 8", "ip": "**********", "port": 55743, "market": "UPX"}, {"group": "GUPX000023", "messageName": "UPX Index Group 9", "ip": "**********", "port": 55744, "market": "UPX"}, {"group": "GUPX000024", "messageName": "UPX Index Group 10", "ip": "**********", "port": 55745, "market": "UPX"}]}