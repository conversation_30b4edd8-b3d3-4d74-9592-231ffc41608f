package com.exchangegw.handler;

import static com.exchangegw.HelperUtils.HelperUtils.updateIndexCache;
import static com.exchangegw.HelperUtils.HelperUtils.updateIndexCacheML;
import static com.exchangegw.HelperUtils.HelperUtils.updateStockCacheD;
import static com.exchangegw.HelperUtils.HelperUtils.updateStockCacheM7;
import static com.exchangegw.HelperUtils.HelperUtils.updateStockCacheMF;
import static com.exchangegw.HelperUtils.HelperUtils.updateStockCacheMX;
import static com.exchangegw.HelperUtils.HelperUtils.updateStockCacheX;

import com.exchangegw.MultiMessage.MessageD;
import com.exchangegw.MultiMessage.MessageFix;
import com.exchangegw.MultiMessage.MessageM1;
import com.exchangegw.MultiMessage.MessageM7;
import com.exchangegw.MultiMessage.MessageMF;
import com.exchangegw.MultiMessage.MessageML;
import com.exchangegw.MultiMessage.MessageMX;
import com.exchangegw.MultiMessage.MessageX;
import com.exchangegw.core.FixParser;
import com.exchangegw.model.IndexInfo;
import com.exchangegw.model.PublishEvent;
import com.exchangegw.model.StockCode;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;


public class STOHandler implements ExchangeMessageHandler {
  @Override
  //public Map<String, String> handleMessage(String exchange, String message, ConcurrentHashMap<String, ConcurrentHashMap<String, StockCode>> stockCache,
  public PublishEvent handleMessage(String exchange, String message, ConcurrentHashMap<String, ConcurrentHashMap<String, StockCode>> stockCache,
      ConcurrentHashMap<String, ConcurrentHashMap<String, IndexInfo>> indexCache) {
    List<String> stock = Arrays.asList("D", "M7", "MF", "M7"); //msgType
    List<String> index = Arrays.asList("M1","ML"); //msgType
    Map<String, String> messagePushServer = new HashMap<>();//result message có thể stock or index
    String msgType = "";
    try {
      MessageFix fixMessage = MessageFix.fromFix(message);
      msgType = fixMessage.getMsgType();
      switch (msgType) {
        case "D":
          MessageD msgD = (MessageD) fixMessage;
          Map<String, String> listFinal = Map.of();
          if(exchange.contains("STX")){
            Map<String,String> msgMap = FixParser.parse(message);
            String exchangeG1 = "STX_G1";
            String exchangeG4 = "STX_G4";
            String boardID = msgMap.get("20004");
            if(boardID != null && boardID.contains( "G1")){
              listFinal = updateStockCacheD(exchangeG1, msgD, stockCache);
            }else if(boardID != null && boardID.contains( "G4")){
              listFinal = updateStockCacheD(exchangeG4, msgD, stockCache);
            }
            // G4: Phân lô lẻ
            // G1: Phân lô chẵn
          } else{
            // Sàn chứng khoán khác
            listFinal = updateStockCacheD(exchange, msgD, stockCache);
          }
          if(!listFinal.isEmpty()){
            messagePushServer = listFinal;
          }
          break;
        //                    case "F":
        //                        MessageF msgF = (MessageF) fixMessage;
        //                        break;
        case "M1":

          MessageM1 msgM1 = (MessageM1) fixMessage;
          Map<String, String> listIndex = updateIndexCache(exchange, msgM1, indexCache);
          if(!listIndex.isEmpty()){
            messagePushServer = listIndex;
          }
          break;
        //                    case "M2":
        //                        MessageM2 msgM2 = (MessageM2) fixMessage;
        ////                        System.out.println("Print MessageM2: "+ msgM2.toString());
        //                        break;
        //                    case "M3":
        //                        MessageM3 msgM3 = (MessageM3) fixMessage;
        ////                        System.out.println("Print MessageM3: "+ msgM3.toString());
        //                        break;
        //                    case "M4":
        //                        MessageM4 msgM4 = (MessageM4) fixMessage;
        ////                        System.out.println("Print MessageM4: "+ msgM4.toString());
        //                        break;
        case "M7":
          //                        // TODO: Cập nhật Sàn/Trần/Tham chiếu từ Sở Chứng khoán | Phân biệt từng lô
          MessageM7 msgM7 = (MessageM7) fixMessage;
          listFinal = Map.of();
          if(exchange.contains("STX")) {
            String exchangeG1 = "STX_G1";
            String exchangeG4 = "STX_G4";
            String boardID = msgM7.getBoardID();
            if(boardID != null && boardID.contains( "G1")){
              listFinal = updateStockCacheM7(exchangeG1, msgM7, stockCache);
            }else if(boardID != null && boardID.contains( "G4")){
              listFinal = updateStockCacheM7(exchangeG4, msgM7, stockCache);
            }
          } else{
            listFinal = updateStockCacheM7(exchange, msgM7, stockCache);
          }
          // gọi push ZeroMQ
          if(!listFinal.isEmpty()){
            messagePushServer = listFinal;
          }
          // Gửi G4: Lô lẻ
          break;
        //                    case "M8":
        //                        MessageM8 msgM8 = (MessageM8) fixMessage;
        //                        System.out.println("Print MessageM8: "+ msgM8.toString());
        //                        break;
        //                    case "MA":
        //                        MessageMA msgMA = (MessageMA) fixMessage;
        //                        System.out.println("Print MessageMA: "+ msgMA.toString());
        //                        break;
        //                    case "MD":
        //                        MessageMD msgMD = (MessageMD) fixMessage;
        //                        System.out.println("Print MessageMD: "+ msgMD.toString());
        //                        break;
        //                    case "ME":
        //                        MessageME msgME = (MessageME) fixMessage;
        //                        System.out.println("Print MessageME: "+ msgME.toString());
        //                        break;
        case "MF":
          // TODO: Tổng room nước ngoài | trong stockCode | 2 lô
          MessageMF msgMF = (MessageMF) fixMessage;
          Map<String, String> listFinalG1 = Map.of();
          Map<String, String> listFinalG4 = Map.of();
          if(exchange.contains("STX")) {
            Map<String, String> msgMap = FixParser.parse(message);
            String exchangeG1 = "STX_G1";
            String exchangeG4 = "STX_G4";
            listFinalG1 = updateStockCacheMF(exchangeG1, msgMF, stockCache);
            listFinalG4 = updateStockCacheMF(exchangeG4, msgMF, stockCache);
          } else{
            listFinalG1 = updateStockCacheMF(exchange, msgMF, stockCache);
          }
          // gọi push ZeroMQ
          if(!listFinalG1.isEmpty()){
            messagePushServer = listFinalG1;
          }
          if(!listFinalG4.isEmpty()){
            messagePushServer = listFinalG4;
          }
          // Gửi G4: Lô lẻ
          break;
        //                    case "MH":
        //                        MessageMH msgMH = (MessageMH) fixMessage;
        ////                        System.out.println("Print MessageMH: "+ msgMH.toString());
        //                        break;
        //                    case "MI":
        //                        MessageMI msgMI = (MessageMI) fixMessage;
        ////                        System.out.println("Print MessageMI: "+ msgMI.toString());
        //                        break;
        case "ML":
          MessageML msgML = (MessageML) fixMessage;
          listIndex = updateIndexCacheML(exchange, msgML, indexCache);
          if(!listIndex.isEmpty()){
            messagePushServer = listIndex;
          }
          break;
        //                    case "MM":
        //                        MessageMM msgMM = (MessageMM) fixMessage;
        ////                        System.out.println("Print MessageMM: "+ msgMM.toString());
        //                        break;
        //                    case "MN":
        //                        MessageMN msgMN = (MessageMN) fixMessage;
        ////                        System.out.println("Print MessageMN: "+ msgMN.toString());
        //                        break;
        //                    case "MO":
        //                        MessageMO msgMO = (MessageMO) fixMessage;
        ////                        System.out.println("Print MessageMO: "+ msgMO.toString());
        //                        break;
        //                    case "MP":
        //                        MessageMP msgMP = (MessageMP) fixMessage;
        ////                        System.out.println("Print MessageMP: "+ msgMP.toString());
        //                        break;
        //                    case "MQ":
        //                        MessageMQ msgMQ = (MessageMQ) fixMessage;
        ////                        System.out.println("Print MessageMQ: "+ msgMQ.toString());
        //                        break;
        //                    case "MR":
        //                        MessageMR msgMR = (MessageMR) fixMessage;
        ////                        System.out.println("Print MessageMR: "+ msgMR.toString());
        //                        break;
        //                    case "MS":
        //                        MessageMS msgMS = (MessageMS) fixMessage;
        ////                        System.out.println("Print MessageMS: "+ msgMS.toString());
        //                        break;
        //                    case "MT":
        //                        MessageMT msgMT = (MessageMT) fixMessage;
        ////                        System.out.println("Print MessageMT: "+ msgMT.toString());
        //                        break;
        //                    case "MU":
        //                        MessageMU msgMU = (MessageMU) fixMessage;
        ////                        System.out.println("Print MessageMU: "+ msgMU.toString());
        //                        break;
        //                    case "MV":
        //                        // TYPE: "SYSTEM"
        //                        MessageMV msgMV = (MessageMV) fixMessage;
        ////                        System.out.println("Print MessageMV: "+ msgMV.toString());
        //                        break;
        //                    case "MW":
        //                        MessageMW msgMW = (MessageMW) fixMessage;
        ////                        System.out.println("Print MessageMW: "+ msgMW.toString());
        //                        break;
        case "MX":
          MessageMX msgMX = (MessageMX) fixMessage;
          // TODO: Cập nhật Sàn/Trần khi kiểm soát thị trường | Từng lô
          listFinal = Map.of();
          if(exchange.contains("STX")) {
            String exchangeG1 = "STX_G1";
            String exchangeG4 = "STX_G4";
            String boardID = msgMX.getBoardID();
            if(boardID != null && boardID.contains( "G1")){
              listFinal = updateStockCacheMX(exchangeG1, msgMX, stockCache);
            }else if(boardID != null && boardID.contains( "G4")){
              listFinal = updateStockCacheMX(exchangeG4, msgMX, stockCache);
            }
          } else{
            listFinal = updateStockCacheMX(exchange, msgMX, stockCache);
          }
          // gọi push ZeroMQ
          if(!listFinal.isEmpty()){
            messagePushServer = listFinal;
          }
          // Gửi G4: Lô lẻ
          break;
        //                    case "W":
        //                        MessageW msgW = (MessageW) fixMessage;
        ////                        System.out.println("Print MessageW: "+ msgW.toString());
        //                        break;
        case "X":
          // TODO: Sức mua bán  | Từng lô
          MessageX msgX = (MessageX) fixMessage;
          Map<String, String> listChange = Map.of();
          if(exchange.contains("STX")) {
            Map<String, String> msgMap = FixParser.parse(message);
            String exchangeG1 = "STX_G1";
            String exchangeG4 = "STX_G4";
            String boardID = msgMap.get("20004");
            if(boardID.contains("G1")){
              listChange = updateStockCacheX(exchangeG1, msgX, stockCache);
            }else if(boardID.contains("G4")){
              listChange = updateStockCacheX(exchangeG4, msgX, stockCache);
            }
          } else{
            listChange = updateStockCacheX(exchange, msgX, stockCache);
          }
          // gọi push ZeroMQ
          if(!listChange.isEmpty()){
            messagePushServer = listChange;
          }
          break;
        default:
          break;
      }
    } catch (Exception e) {
      System.err.println("Parse or handling failed for FIX message: " + e.getMessage());
    }
    //
    if(!msgType.isEmpty() && !messagePushServer.isEmpty()){
      if(stock.contains(msgType)){
        ObjectMapper mapper = new ObjectMapper();
        Object data = mapper.convertValue(messagePushServer, Object.class);
        return PublishEvent.builder()
            .type("stock")
            .data(data)
            .build();
      }
      if(index.contains(msgType)){
        ObjectMapper mapper = new ObjectMapper();
        Object data = mapper.convertValue(messagePushServer, Object.class);
        return PublishEvent.builder()
            .type("index")
            .data(data)
            .build();
      }
    }
    return null;
  }
}
