package com.exchangegw.handler;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import com.exchangegw.model.IndexInfo;
import com.exchangegw.model.PublishEvent;
import com.exchangegw.model.StockCode;

public interface ExchangeMessageHandler {
//    Map<String, String> handleMessage(String exchange, String message,ConcurrentHashMap<String, ConcurrentHashMap<String, StockCode>> stockCache,
PublishEvent handleMessage(String exchange, String message, ConcurrentHashMap<String, ConcurrentHashMap<String, StockCode>> stockCache,
                           ConcurrentHashMap<String, ConcurrentHashMap<String, IndexInfo>> indexCache);
}

