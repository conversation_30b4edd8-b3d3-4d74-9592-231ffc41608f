package com.exchangegw.handler;

public class ExchangeHandlerFactory {
    public static ExchangeMessageHandler getHandler(String exchange) {
        return switch (exchange) {
//            case "BDX" -> new BDXHandler();
//            case "DVX" -> new DVXHandler();
//            case "HCX" -> new HCXHandler();
            case "STO" -> new STOHandler();
            case "STX" -> new STXHandler();
            case "UPX" -> new UPXHandler();
          default -> null;
        };
    }
}
