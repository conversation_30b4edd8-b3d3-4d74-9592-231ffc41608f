package com.exchangegw.core;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.IOException;
import java.net.DatagramPacket;
import java.net.DatagramSocket;
import java.net.InetAddress;
import java.net.InetSocketAddress;
import java.net.MulticastSocket;
import java.net.NetworkInterface;
import java.net.SocketTimeoutException;
import java.util.Enumeration;
import java.util.HashMap;

import com.exchangegw.config.FixFeedConfig;
import com.exchangegw.queue.MddsMessageQueue;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class FixWorker implements Runnable {
    private static final Logger logger = LoggerFactory.getLogger(FixWorker.class);
    private final FixFeedConfig config;
    private final MddsMessageQueue messageQueue;
    private final String ipListen;

    public FixWorker(String _ipListen, FixFeedConfig config, MddsMessageQueue _messageQueue){
        this.config = config;
        this.messageQueue = _messageQueue;
        ipListen = _ipListen;
    }

    @Override
    public void run(){
        String ip = config.getIp();
        int port = config.getPort();
        DatagramSocket socket = null;
        ObjectMapper mapper = new ObjectMapper();
        try {
            InetAddress address = InetAddress.getByName(ip);
            if(address.isMulticastAddress()){      
                MulticastSocket mSocket = new MulticastSocket(port);
                // Lấy interface đúng (ví dụ: theo tên cấu hình hoặc interface đầu tiên có multicast)
                NetworkInterface networkInterface = NetworkInterface.getByName(ipListen);
                if (networkInterface == null) {
                    // fallback lấy interface đầu tiên có multicast
                    Enumeration<NetworkInterface> interfaces = NetworkInterface.getNetworkInterfaces();
                    while (interfaces.hasMoreElements()) {
                        NetworkInterface ni = interfaces.nextElement();
                        if (ni.supportsMulticast() && !ni.isLoopback() && ni.isUp()) {
                            networkInterface = ni;
                            break;
                        }
                    }
                }
                if (networkInterface == null) {
                    throw new IOException("Không tìm thấy interface multicast hợp lệ.");
                }
                mSocket.joinGroup(new InetSocketAddress(address, port), networkInterface);
                socket = mSocket;
                logger.info("[Multicast] Joined group {}:{} via interface {}", ip, port, networkInterface.getName());
        
            }else{
                socket = new DatagramSocket(port);
                logger.info("Listening on unicast {} : {}", ip, port);
            }
            byte[] buffer = new byte[8192];
            DatagramPacket packet = new DatagramPacket(buffer, buffer.length);
            Integer messageCount = 0;
            while (true) {
//                socket.receive(packet);
//                HashMap<String, String> fixMessage = new HashMap<>();
//                fixMessage.put("group", config.getGroup());
//                fixMessage.put("message", new String(packet.getData(), 0, packet.getLength(), "UTF-8"));
//                messageQueue.addMessage(fixMessage);
                try {
                    if(packet.getLength() == 0) {
                        continue;
                    };
                    messageCount++;
                    socket.receive(packet);
                    String msg = new String(packet.getData(), 0, packet.getLength(), "UTF-8");
                    String group =  config.getGroup();
                    if( group !=null){
                        HashMap<String, String> input = new HashMap<>();
                        input.put("STT", messageCount+"");
                        input.put("group", group);
                        input.put("message",  msg);
                        messageQueue.addMessage(input);
                    }else{
                        logger.error(">>>>>>>>>>> Lỗi input: <<<<<<<<<<<<");
                        logger.error("msg: {}", msg);
                        logger.error("group: {}", group);
                        logger.error(">>>>>>>>>>> kết thúc: <<<<<<<<<<<<");

                    }
                    // thực hiện map
                    // queue > lưu cache theo key -> Group -> object theo msgType d,f, MV,...
                } catch (SocketTimeoutException e) {
                    logger.warn("Timeout waiting for messages...");
                } catch (Exception e) {
                    logger.error("Error receiving message: {}", e.getMessage(), e);
                } finally{
                    packet.setLength(buffer.length);
                }
            }
            
        } catch (Exception e) {
            logger.error("Error listen MDDS in : {} : {}", config.toString(), e.getMessage(), e);
        } finally {
            if(socket!=null && !socket.isClosed())
                socket.close();
        }
    }
}
