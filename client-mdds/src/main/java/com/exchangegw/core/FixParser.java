package com.exchangegw.core;

import java.util.HashMap;
import java.util.Map;

public class FixParser {
    /**
     * Phân tích thông điệp FIX thô và trả về Map các trường.
     * Các trường không có giá trị sẽ không xuất hiện trong Map.
     *
     * @param fixMessage Chuỗi FIX (có thể chứa ký tự SOH hoặc '|' phân cách)
     * @return Map từ tag (String) sang giá trị (String).
     */
    public static Map<String, String> parse(String fixMessage) {
        Map<String, String> fields = new HashMap<>();
        if (fixMessage == null || fixMessage.isEmpty()) {
            return fields;
        }
        // Thay '|' thành ký tự SOH (nếu dữ liệu sử dụng '|' để hiển thị)
        String sohMessage = fixMessage.replace('|', '\u0001');
        // Tách các cặp tag=value bởi ký tự SOH
        String[] pairs = sohMessage.split("\u0001");
        for (String pair : pairs) {
            if (pair.isEmpty()) continue;
            String[] kv = pair.split("=", 2);
            if (kv.length == 2) {
                fields.put(kv[0], kv[1]);
            }
        }
        return fields;
    }
}