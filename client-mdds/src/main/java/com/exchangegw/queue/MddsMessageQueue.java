package com.exchangegw.queue;

import java.util.HashMap;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;

public class MddsMessageQueue {
    private final BlockingQueue<HashMap<String,String>> rawMessages = new LinkedBlockingQueue<>(1000000);

    // Thêm message vào hàng đợi
    public void addMessage(HashMap<String, String> message) {
        if (message != null && !message.isEmpty()) {
            rawMessages.offer(message);
        }
    }
    // Lấy message ra (không chờ)
    public HashMap<String, String> pollMessage() {
        return rawMessages.poll();
    }
    // Lấy message ra (đợi nếu rỗng)
    public HashMap<String, String> takeMessage() throws InterruptedException {
        return rawMessages.take();
    }
    // Số lượng message hiện tại
    public int getQueueSize() {
        return rawMessages.size();
    }
    // Ki<PERSON><PERSON> tra hàng đợi có rỗng không
    public boolean isEmpty() {
        return rawMessages.isEmpty();
    }
}