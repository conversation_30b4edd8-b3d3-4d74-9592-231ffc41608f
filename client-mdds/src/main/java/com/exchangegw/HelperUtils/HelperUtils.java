package com.exchangegw.HelperUtils;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import com.exchangegw.MultiMessage.*;
import com.exchangegw.model.IndexInfo;
import com.exchangegw.model.StockCode;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;

public class HelperUtils {
    public static String convertHashMapToJson(HashMap<String, String> mapperString){
        ObjectMapper mapper = new ObjectMapper();
        try {
            return mapper.writeValueAsString(mapperString);
        } catch (JsonProcessingException e) {
           return "";
        }
    }
    public static Boolean getPropertyValue(Object data, String propertyName) {
        if (data == null) return false;
        try {
            Map<?, ?> map = (Map<?, ?>) data;
            int size = ((Map<?, ?>) data).size();
            Object stockCode = map.get(propertyName);
          return stockCode != null && size > 2;
        } catch (Exception e) {
            return false;
        }
    }

    public static String convertStringUTCto7 (String input) {
        try {
            DateTimeFormatter inputFormatter = DateTimeFormatter.ofPattern("yyyyMMdd HH:mm:ss.SSS");
            LocalDateTime localDateTime = LocalDateTime.parse(input, inputFormatter);
            ZonedDateTime utcZoned = localDateTime.atZone(ZoneId.of("UTC"));
            ZonedDateTime utcZoned7 = utcZoned.withZoneSameInstant(ZoneId.of("Asia/Ho_Chi_Minh"));
            DateTimeFormatter outputFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
            return utcZoned7.format(outputFormatter);
        } catch (Exception e) {
            return null;
        }
    }

    public static Map<String, String> convertToMap(StockCode data) {
        Map<String, String> map = new HashMap<>();
        if (data == null) return map;
        for (Field field : StockCode.class.getDeclaredFields()) {
            field.setAccessible(true);
            try {
                Object val = field.get(data);
                if(val != null) map.put(field.getName(), String.valueOf(val));                
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return map;
    }

    public static List<Map<String, String>> parseRepeatingGroups(String fixMessage) {
        List<Map<String, String>> groups = new ArrayList<>();
        String[] parts = fixMessage.split(""); // Dấu SOH: đúng định dạng FIX
        Map<String, String> currentGroup = null;
        for (String part : parts) {
            String[] kv = part.split("=", 2);
            if (kv.length != 2) continue;
            String tag = kv[0];
            String value = kv[1];
            // Bắt đầu nhóm mới khi gặp tag 83
            if ("83".equals(tag)) {
                if (currentGroup != null && !currentGroup.isEmpty()) {
                    groups.add(currentGroup);
                }
                currentGroup = new HashMap<>();
            }
            // Nếu đang trong group, thu thập các tag liên quan
            if (currentGroup != null && Arrays.asList("83", "279", "269", "290", "270", "271", "346", "30271").contains(tag)) {
                currentGroup.put(tag, value);
            }
        }
        // Thêm nhóm cuối cùng nếu có
        if (currentGroup != null && !currentGroup.isEmpty()) {
            groups.add(currentGroup);
        }
        return groups;
    }

    public static List<Map<String, String>> parseMDEntries(String rawFixMessage, int expectedEntries) {
            List<Map<String, String>> result = new ArrayList<>();
            String[] tokens = rawFixMessage.split("\\|");
            Map<String, String> currentGroup = null;
            int foundEntries = 0;
            for (String token : tokens) {
                String[] kv = token.split("=", 2);
                if (kv.length != 2) continue;
                String tag = kv[0];
                String value = kv[1];
                // Check if tag is RptSeq (83), which marks start of a group
                if (tag.equals("83")) {
                    if (currentGroup != null && !currentGroup.isEmpty()) {
                        result.add(currentGroup);
                        foundEntries++;
                        if (foundEntries == expectedEntries) break;
                    }
                    currentGroup = new HashMap<>();
                }
                if (currentGroup != null) {
                    currentGroup.put(tag, value);
                }
            }
            // Add last entry if not added
            if (currentGroup != null && !currentGroup.isEmpty() && foundEntries < expectedEntries) {
                result.add(currentGroup);
            }
            return result;
        }

    // So sánh field
    public static List<String> getChangedFields(Object oldObj, Object newObj) {
        List<String> changedFields = new ArrayList<>();
        if (oldObj == null || newObj == null || !oldObj.getClass().equals(newObj.getClass())) {
            return changedFields;
        }

        Field[] fields = oldObj.getClass().getDeclaredFields();

        for (Field field : fields) {
            field.setAccessible(true);
            try {
                Object oldValue = field.get(oldObj);
                Object newValue = field.get(newObj);
                if (!Objects.equals(oldValue, newValue)) {
                    changedFields.add(field.getName() + ": [" + oldValue + "] -> [" + newValue + "]");
                }
            } catch (IllegalAccessException e) {
                // Optional: Log or handle error
            }
        }
        return changedFields;
    }

    public static Map<String, String> getChangedFieldMap(Object oldObj, Object newObj) {
        Map<String, String> changedNewValues = new LinkedHashMap<>();

        if (oldObj == null || newObj == null || !oldObj.getClass().equals(newObj.getClass())) {
            return changedNewValues;
        }

        Field[] fields = oldObj.getClass().getDeclaredFields();

        for (Field field : fields) {
            field.setAccessible(true);
            try {
                Object oldValue = field.get(oldObj);
                Object newValue = field.get(newObj);
                if (!Objects.equals(oldValue, newValue)) {
                    changedNewValues.put(field.getName(), String.valueOf(newValue)); // ép về String
                }
            } catch (IllegalAccessException e) {
                // Optional: log or handle
            }
        }

        return changedNewValues;
    }

    public static Map<String, String> updateStockCacheX(
            String exchange,
            MessageX msgX,
            Map<String, ConcurrentHashMap<String, StockCode>> stockCache
    ) {
        // 2. Lấy hoặc tạo map cổ phiếu theo sàn
        ConcurrentHashMap<String, StockCode> listStock =
                stockCache.computeIfAbsent(exchange, k -> new ConcurrentHashMap<>());

        // 3. Lấy key và object cũ
        String stockKey = msgX.getSymbol();
        StockCode old = listStock.get(stockKey) == null ? new StockCode() :  listStock.get(stockKey);
        StockCode cur = old != null ? old.clone() : new StockCode();

        // 4. Gán dữ liệu mới từ MsgDmsgMF
        cur.stockIsin = msgX.getSymbol();                                      // T55
        cur.marketID = msgX.getMarketID();                                     // T30001
        // xử lý BID (Bán) - Ask (Mua)
        List<MessageX.MDEntry> listBidOff = msgX.getMdEntries();
        // MDEntryType: 0 mua | 1 bán

        List<MessageX.MDEntry> listBid = listBidOff.stream()
                .filter((entry) -> "0".equals(entry.getMDEntryType()))
                .sorted(Comparator.comparing(MessageX.MDEntry::getMDEntryPx).reversed()) // giảm dần
                .toList();

        List<MessageX.MDEntry> listAsk = listBidOff.stream()
                .filter((entry) -> "1".equals(entry.getMDEntryType()))
                .sorted(Comparator.comparing(MessageX.MDEntry::getMDEntryPx).reversed()) // giảm dần
                .toList();
        MessageX.MDEntry order = !listBidOff.isEmpty() ? listBidOff.get(0) : null;

        // giá khớp hiện tại
        if (order != null) {
            cur.matchPrice = order.getMDEntryPx();
            cur.matchVolume = BigDecimal.valueOf(order.getMDEntrySize());
        }
        if (cur.refPrice == null) {
            cur.refPrice = BigDecimal.ZERO;
        }
        if (cur.matchPrice == null) {
            cur.matchPrice = BigDecimal.ZERO;
        }
        BigDecimal per = BigDecimal.ZERO;

        if (cur.refPrice.compareTo(BigDecimal.ZERO) != 0) {
            per = cur.refPrice.subtract(cur.matchPrice) // (refPrice - matchPrice)
                .divide(cur.refPrice, 6, RoundingMode.HALF_UP) // chia refPrice
                .multiply(BigDecimal.valueOf(100)); // nhân 100 để ra %
        }
        cur.matchPer = per.floatValue();
        cur.matchPer = (cur.matchPer  == 100.0f) ? 0f : cur.matchPer;


        // xử lý map dữ liệu
        for (int i = 0; i < Math.min(3, listBid.size()); i++) {
            MessageX.MDEntry bidInfo = listBid.get(i);
            switch (i) {
                case 0 -> {
                    cur.purPrice1 = bidInfo.getMDEntryPx();
                    cur.purVolume1 = bidInfo.getMDEntrySize();
                }
                case 1 -> {
                    cur.purPrice2 = bidInfo.getMDEntryPx();
                    cur.purVolume2 = bidInfo.getMDEntrySize();
                }
                case 2 -> {
                    cur.purPrice3 = bidInfo.getMDEntryPx();
                    cur.purVolume3 = bidInfo.getMDEntrySize();
                }
            }
        }

        for (int i = 0; i < Math.min(3, listAsk.size()); i++) {
            MessageX.MDEntry askInfo = listAsk.get(i);
            switch (i) {
                case 0 -> {
                    cur.sellPrice1 = askInfo.getMDEntryPx();
                    cur.sellVolume1 = askInfo.getMDEntrySize();
                }
                case 1 -> {
                    cur.sellPrice2 = askInfo.getMDEntryPx();
                    cur.sellVolume2 = askInfo.getMDEntrySize();
                }
                case 2 -> {
                    cur.sellPrice3 = askInfo.getMDEntryPx();
                    cur.sellVolume3 = askInfo.getMDEntrySize();
                }
            }
        }

        // 5. So sánh thay đổi
        Map<String, String> changes = HelperUtils.getChangedFieldMap(old, cur);
        changes.put("stockCode",cur.getStockCode());
        changes.put("exchange",exchange);

        // 6. Cập nhật cache
        listStock.put(stockKey, cur);
        stockCache.put(exchange, listStock);
        return changes;
    }

    // Cập nhật message D
    public static Map<String, String> updateStockCacheD(
            String exchange,
            MessageD msgD,
            Map<String, ConcurrentHashMap<String, StockCode>> stockCache
    ) {
        // 2. Lấy hoặc tạo map cổ phiếu theo sàn
        ConcurrentHashMap<String, StockCode> listStock =
                stockCache.computeIfAbsent(exchange, k -> new ConcurrentHashMap<>());

        // 3. Lấy key và object cũ
        String stockKey = msgD.getSymbol();
        StockCode old = listStock.get(stockKey) == null ? new StockCode() :  listStock.get(stockKey);
        StockCode cur = old != null ? old.clone() : new StockCode();

        // 4. Gán dữ liệu mới từ MsgD
        cur.stockIsin = msgD.getSymbol();             // T55
        cur.marketID  = msgD.getMarketID();           // T30001
        cur.exchange  = msgD.getSecurityExchange();   // T207
        cur.stockCode = msgD.getTickerCode();         // T30624
        cur.ceiPrice  = msgD.getHighLimitPrice();     // T1149
        cur.floPrice  = msgD.getLowLimitPrice();      // T1148
        cur.refPrice  = msgD.getReferencePrice();     // T20013

        // 5. So sánh thay đổi
        Map<String, String> changes = HelperUtils.getChangedFieldMap(old, cur);
        changes.put("stockCode",cur.getStockCode());
        changes.put("exchange",exchange);

        // 6. Cập nhật cache
        listStock.put(stockKey, cur);
        stockCache.put(exchange, listStock);

        return changes;
    }

    // Cập nhật message MF
    public static Map<String, String>  updateStockCacheMF(
            String exchange,
            MessageMF msgMF,
            Map<String, ConcurrentHashMap<String, StockCode>> stockCache
    ) {
        // 2. Lấy hoặc tạo map cổ phiếu theo sàn
        ConcurrentHashMap<String, StockCode> listStock =
                stockCache.computeIfAbsent(exchange, k -> new ConcurrentHashMap<>());

        // 3. Lấy key và object cũ
        String stockKey = msgMF.getSymbol();
        StockCode old = listStock.get(stockKey) == null ? new StockCode() :  listStock.get(stockKey);
        StockCode cur = old != null ? old.clone() : new StockCode();

        // 4. Gán dữ liệu mới từ MsgD
        cur.foreignerBuyPosblQty = msgMF.getForeignerBuyPosblQty();             // T30557
        cur.stockIsin = msgMF.getSymbol();                                      // T55
        cur.marketID = msgMF.getMarketID();                                     // T30001

        // 5. So sánh thay đổi
        Map<String, String> changes = HelperUtils.getChangedFieldMap(old, cur);
        changes.put("stockCode",cur.getStockCode());
        changes.put("exchange",exchange);

        // 6. Cập nhật cache
        listStock.put(stockKey, cur);
        stockCache.put(exchange, listStock);

        return changes;
    }

    public static Map<String, String>  updateStockCacheMX(
            String exchange,
            MessageMX msgMX,
            Map<String, ConcurrentHashMap<String, StockCode>> stockCache
    ) {
        // 2. Lấy hoặc tạo map cổ phiếu theo sàn
        ConcurrentHashMap<String, StockCode> listStock =
                stockCache.computeIfAbsent(exchange, k -> new ConcurrentHashMap<>());

        // 3. Lấy key và object cũ
        String stockKey = msgMX.getSymbol();
        StockCode old = listStock.get(stockKey) == null ? new StockCode() :  listStock.get(stockKey);
        StockCode cur = old != null ? old.clone() : new StockCode();

        // 4. Gán dữ liệu mới từ MsgD
        cur.stockIsin = msgMX.getSymbol();                                      // T55
        cur.marketID  = msgMX.getMarketID();                                    // T30001
        cur.hightPrice = msgMX.getHighLimitPrice();                             // T1149
        cur.lowPrice   = msgMX.getLowLimitPrice();                              // T1148

        // 5. So sánh thay đổi
        Map<String, String> changes = HelperUtils.getChangedFieldMap(old, cur);
        changes.put("stockCode",cur.getStockCode());
        changes.put("exchange",exchange);

        // 6. Cập nhật cache
        listStock.put(stockKey, cur);
        stockCache.put(exchange, listStock);
        return changes;
    }

    public static Map<String, String> updateStockCacheM7(
            String exchange,
            MessageM7 msgM7,
            Map<String, ConcurrentHashMap<String, StockCode>> stockCache
    ) {
        // 2. Lấy hoặc tạo map cổ phiếu theo sàn
        ConcurrentHashMap<String, StockCode> listStock =
                stockCache.computeIfAbsent(exchange, k -> new ConcurrentHashMap<>());

        // 3. Lấy key và object cũ
        String stockKey = msgM7.getSymbol();
        StockCode old = listStock.get(stockKey) == null ? new StockCode() :  listStock.get(stockKey);
        StockCode cur = old != null ? old.clone() : new StockCode();

        // 4. Gán dữ liệu mới từ MsgD
        cur.stockIsin = msgM7.getSymbol();                                      // T55
        cur.marketID  = msgM7.getMarketID();                                    // T30001
        cur.ceiPrice  = msgM7.getHighLimitPrice();       // T1149
        cur.floPrice  = msgM7.getLowLimitPrice();        // T1148
        cur.refPrice  = msgM7.getReferencePrice();       // T20013
        cur.hightPrice  = msgM7.getHighestOrderPrice();  // T20015
        cur.lowPrice  = msgM7.getLowestOrderPrice();     // T20016

        // 5. So sánh thay đổi
        Map<String, String> changes = HelperUtils.getChangedFieldMap(old, cur);
        changes.put("stockCode",cur.getStockCode());
        changes.put("exchange",exchange);

        // 6. Cập nhật cache
        listStock.put(stockKey, cur);
        stockCache.put(exchange, listStock);

        return changes;
    }

    // Index
    public static Map<String, String> updateIndexCacheML(
        String exchange,
        MessageML msgML,
        ConcurrentHashMap<String, ConcurrentHashMap<String, IndexInfo>> indexCache
    ) {
        if (exchange == null || msgML == null || indexCache == null) {
            return Collections.emptyMap();
        }
        try {
            // 1. Lấy cache theo sàn
            ConcurrentHashMap<String, IndexInfo> listIndex =
                indexCache.computeIfAbsent(exchange, k -> new ConcurrentHashMap<>());

            // 2. Lấy bản cũ và tạo bản mới
            String indexKey = msgML.getIndexsTypeCode();
            IndexInfo old = listIndex.get(indexKey) == null ? new IndexInfo() : listIndex.get(indexKey) ;
            IndexInfo cur = old != null ? old.clone() : new IndexInfo();

            // 3. Cập nhật từ msgM1
            if(msgML.getMarketID().contains("STX")){
                cur.setExchange(msgML.getMarketID()+"_G1");
            }else{
                cur.setExchange(msgML.getMarketID());
            }
            cur.setMarketID(msgML.getMarketID());
            cur.setIndexsTypeCode(indexKey);
            cur.setIndexsTypeEngName(msgML.getIdxEnglishName());
            cur.setIndexsTypeName(msgML.getIdxName());
//            cur.setSendingTime(msgML.getSendingTime());

            // 4. So sánh thay đổi
            Map<String, String> changes = HelperUtils.getChangedFieldMap(old, cur);
            changes.put("exchange",cur.getExchange());
            changes.put("marketID",cur.getMarketID());
            changes.put("indexsTypeCode",cur.getIndexsTypeCode());
            changes.put("sendingTime",cur.getSendingTime());
            if(!cur.getIndexsTypeEngName().isEmpty()){
               changes.put("indexsTypeEngName",cur.getIndexsTypeEngName());
            }
            if(!cur.getIndexsTypeName().isEmpty()){
                changes.put("indexsTypeName",cur.getIndexsTypeName());
            }
            // 5. Cập nhật lại cache
            listIndex.put(indexKey, cur);
            indexCache.put(exchange, listIndex);
            return changes;
        } catch (Exception ex) {
            ex.printStackTrace(); // logging thật trong prod
            return Collections.emptyMap();
        }
    }

    public static Map<String, String> updateIndexCache(
            String exchange,
            MessageM1 msgM1,
            ConcurrentHashMap<String, ConcurrentHashMap<String, IndexInfo>> indexCache
    ) {
        if (exchange == null || msgM1 == null || indexCache == null) {
            return Collections.emptyMap();
        }
        try {
            // 1. Lấy cache theo sàn
            ConcurrentHashMap<String, IndexInfo> listIndex =
                    indexCache.computeIfAbsent(exchange, k -> new ConcurrentHashMap<>());

            // 2. Lấy bản cũ và tạo bản mới
            String indexKey = msgM1.getIndexsTypeCode();
            IndexInfo old = listIndex.get(indexKey) == null ? new IndexInfo() : listIndex.get(indexKey) ;
            IndexInfo cur = old != null ? old.clone() : new IndexInfo();

            // 3. Cập nhật từ msgM1
            if(msgM1.getMarketID().contains("STX")){
                cur.setExchange(msgM1.getMarketID()+"_G1");
            }else{
                cur.setExchange(msgM1.getMarketID());
            }
            cur.setMarketID(msgM1.getMarketID());
            cur.setIndexsTypeCode(indexKey);

            cur.setTotalVolumeTraded(msgM1.getTotalVolumeTraded());
            cur.setGrossTradeAmt(msgM1.getGrossTradeAmt());

            cur.setFluctuationUpIssueCount(msgM1.getFluctuationUpIssueCount());
            cur.setFluctuationSteadinessIssueCount(msgM1.getFluctuationSteadinessIssueCount());
            cur.setFluctuationDownIssueCount(msgM1.getFluctuationDownIssueCount());
            cur.setFluctuationLowerLimitIssueCount(msgM1.getFluctuationLowerLimitIssueCount());
            cur.setFluctuationUpperLimitIssueCount(msgM1.getFluctuationUpperLimitIssueCount());
            if(!msgM1.getSendingTime().isEmpty()){
                // Chuyển String Date -> + 7:00
                String[] time = Objects.requireNonNull(convertStringUTCto7(msgM1.getSendingTime())).split("\\s");
                if(time.length > 1){
                    cur.setSendingTime(time[1]);
                }
            }

            cur.setValueIndexes(msgM1.getValueIndexes());
            // 4. So sánh thay đổi
            Map<String, String> changes = HelperUtils.getChangedFieldMap(old, cur);
            changes.put("exchange",cur.getExchange());
            changes.put("marketID",cur.getMarketID());
            changes.put("indexsTypeCode",cur.getIndexsTypeCode());
            changes.put("sendingTime",cur.getSendingTime());

            // 5. Cập nhật lại cache
            listIndex.put(indexKey, cur);
            indexCache.put(exchange, listIndex);
            return changes;
        } catch (Exception ex) {
            ex.printStackTrace(); // logging thật trong prod
            return Collections.emptyMap();
        }
    }
}
