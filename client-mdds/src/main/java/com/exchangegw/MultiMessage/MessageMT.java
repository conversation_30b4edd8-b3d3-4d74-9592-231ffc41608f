package com.exchangegw.MultiMessage;

import java.time.LocalTime;
import java.util.HashMap;
import java.util.Map;

import com.exchangegw.HelperUtils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class MessageMT implements MessageFix{
    private String msgType = "MT";  // FIX MsgType

    @JsonProperty("MarketID")
    private String marketID;
    @JsonProperty("BoardID")
    private String boardID;
    @JsonProperty("TradingSessionID")
    private String tradingSessionID;
    @JsonProperty("Symbol")
    private String symbol;
    @JsonProperty("TransactTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "HHmmssSSS")
    private LocalTime transactTime; 
    @JsonProperty("FornInvestTypeCode")
    private String fornInvestTypeCode;
    @JsonProperty("SellVolume")
    private Integer sellVolume;
    @JsonProperty("SellTradeAmount")
    private Float sellTradeAmount;
    @JsonProperty("BuyVolume")
    private Integer buyVolume;
    @JsonProperty("BuyTradedAmount")
    private Float buyTradedAmount;
    @JsonProperty("SellVolumeTotal")
    private Integer sellVolumeTotal;
    @JsonProperty("SellTradeAmountTotal")
    private Float sellTradeAmountTotal;
    @JsonProperty("BuyVolumeTotal")
    private Integer buyVolumeTotal;
    @JsonProperty("BuytradedAmountTotal")
    private Float buytradedAmountTotal;
    // Dữ liệu sở UTC
    @JsonProperty("SendingTime")
    private String sendingTime; //

    public static MessageMT fromMap(Map<String, String> fixFields) {
        Map<String, String> map = new HashMap<>();
        map.put("MarketID", fixFields.get("30001"));
        map.put("BoardID", fixFields.get("20004"));
        map.put("TradingSessionID", fixFields.get("336"));
        map.put("Symbol", fixFields.get("55"));
        map.put("TransactTime", fixFields.get("60"));
        map.put("FornInvestTypeCode", fixFields.get("20054"));
        map.put("SellVolume", fixFields.get("331"));
        map.put("SellTradeAmount", fixFields.get("30168"));
        map.put("BuyVolume", fixFields.get("330"));
        map.put("BuyTradedAmount", fixFields.get("30169"));
        map.put("SellVolumeTotal", fixFields.get("30643"));
        map.put("SellTradeAmountTotal", fixFields.get("30644"));
        map.put("BuyVolumeTotal", fixFields.get("30645"));
        map.put("BuytradedAmountTotal", fixFields.get("30646"));
        map.put("SendingTime", fixFields.get("52"));

        return JsonUtils.MAPPER.convertValue(map, MessageMT.class);
    }
}
