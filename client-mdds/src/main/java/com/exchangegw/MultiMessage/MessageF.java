package com.exchangegw.MultiMessage;

import java.util.HashMap;
import java.util.Map;

import com.exchangegw.HelperUtils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
@Data
public class MessageF implements MessageFix {
    private String msgType = "F";  // FIX MsgType

    @JsonProperty("MarketID")
    private String marketID; //Sở giao dịch chứng khoán
    @JsonProperty("TscprodGrpId")
    private String tscprodGrpId;
    @JsonProperty("BoardID")
    private String boardID; //ID Bảng giao dịch
    @JsonProperty("BoardEvtID")
    private String boardEvtID;
    @JsonProperty("SessOpenCloseCode")
    private String sessOpenCloseCode;
    @JsonProperty("Symbol")
    private String symbol;
    @JsonProperty("TradingSessionID")
    private String tradingSessionID;
    @JsonProperty("HaltRsnCode")
    private String haltRsnCode;

    // Dữ liệu sở UTC
    @JsonProperty("SendingTime")
    private String sendingTime; //


    public static MessageF fromMap(Map<String, String> fixFields) {
        Map<String, String> map = new HashMap<>();
        map.put("MarketID", fixFields.get("30001"));
        map.put("TscprodGrpId", fixFields.get("30648"));
        map.put("BoardID", fixFields.get("20004"));
        map.put("BoardEvtID", fixFields.get("20005"));
        map.put("SessOpenCloseCode", fixFields.get("20008"));
        map.put("Symbol", fixFields.get("55"));
        map.put("TradingSessionID", fixFields.get("336"));
        map.put("HaltRsnCode", fixFields.get("30651"));
        map.put("SendingTime", fixFields.get("52"));

        return JsonUtils.MAPPER.convertValue(map, MessageF.class);
    }
}
