package com.exchangegw.MultiMessage;

import java.math.BigDecimal;
import java.time.LocalTime;
import java.util.HashMap;
import java.util.Map;

import com.exchangegw.HelperUtils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
public class MessageM1 implements MessageFix {
    private String msgType = "M1";  // FIX MsgType

    @JsonProperty("MarketID")
    private String marketID;  // Tag 30001
    @JsonProperty("TradingSessionID")
    private String tradingSessionID;  // Tag 336
    @JsonProperty("MarketIndexClass")
    private String marketIndexClass;  // Tag 30569
    @JsonProperty("IndexsTypeCode")
    private String indexsTypeCode;  // Tag 30167
    @JsonProperty("Currency")
    private String crrency;  // Tag 15
    @JsonProperty("TransactTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "HHmmssSSS")
    private LocalTime transactTime;  // Tag 60 (UTC time)
    @JsonProperty("ValueIndexes")
    private Float valueIndexes;  // Tag 30217
    @JsonProperty("TotalVolumeTraded")
    private BigDecimal totalVolumeTraded;  // Tag 387
    @JsonProperty("GrossTradeAmt")
    private BigDecimal grossTradeAmt;  // Tag 381
    @JsonProperty("ContauctAccTrdvol")
    private Integer contauctAccTrdvol;  // Tag 30638
    @JsonProperty("ContauctAccTrdval")
    private Float contauctAccTrdval;  // Tag 30639
    @JsonProperty("BlktrdAccTrdvol")
    private Integer blktrdAccTrdvol;  // Tag 30640
    @JsonProperty("BlktrdAccTrdval")
    private Float blktrdAccTrdval;  // Tag 30641
    @JsonProperty("FluctuationUpperLimitIssueCount")
    private Integer fluctuationUpperLimitIssueCount;  // Tag 30589
    @JsonProperty("FluctuationUpIssueCount")
    private Integer fluctuationUpIssueCount;  // Tag 30590
    @JsonProperty("FluctuationSteadinessIssueCount")
    private Integer fluctuationSteadinessIssueCount;  // Tag 30591
    @JsonProperty("FluctuationDownIssueCount")
    private Integer fluctuationDownIssueCount;  // Tag 30592
    @JsonProperty("FluctuationLowerLimitIssueCount")
    private Integer fluctuationLowerLimitIssueCount;  // Tag 30593
    @JsonProperty("FluctuationUpIssueVolume")
    private Integer fluctuationUpIssueVolume;  // Tag 30594
    @JsonProperty("FluctuationDownIssueVolume")
    private Integer fluctuationDownIssueVolume;  // Tag 30595
    @JsonProperty("FluctuationSteadinessIssueVolume")
    private Integer fluctuationSteadinessIssueVolume;  // Tag 30596

    @JsonProperty("SendingTime")
    private String sendingTime; //

     public static MessageM1 fromMap(Map<String, String> fixFields) {
        Map<String, String> map = new HashMap<>();
        map.put("MarketID", fixFields.get("30001"));
        map.put("TradingSessionID", fixFields.get("336"));
        map.put("MarketIndexClass", fixFields.get("30569"));
        map.put("IndexsTypeCode", fixFields.get("30167"));
        map.put("Currency", fixFields.get("15"));
        map.put("TransactTime", fixFields.get("60"));
        map.put("ValueIndexes", fixFields.get("30217"));
        map.put("TotalVolumeTraded", fixFields.get("387"));
        map.put("GrossTradeAmt", fixFields.get("381"));
        map.put("ContauctAccTrdvol", fixFields.get("30638"));
        map.put("ContauctAccTrdval", fixFields.get("30639"));
        map.put("BlktrdAccTrdvol", fixFields.get("30640"));
        map.put("BlktrdAccTrdval", fixFields.get("30641"));
        map.put("FluctuationUpperLimitIssueCount", fixFields.get("30589"));
        map.put("FluctuationUpIssueCount", fixFields.get("30590"));
        map.put("FluctuationSteadinessIssueCount", fixFields.get("30591"));
        map.put("FluctuationDownIssueCount", fixFields.get("30592"));
        map.put("FluctuationLowerLimitIssueCount", fixFields.get("30593"));
        map.put("FluctuationUpIssueVolume", fixFields.get("30594"));
        map.put("FluctuationDownIssueVolume", fixFields.get("30595"));
        map.put("FluctuationSteadinessIssueVolume", fixFields.get("30596"));

        // Header
       map.put("SendingTime", fixFields.get("52"));
       return JsonUtils.MAPPER.convertValue(map, MessageM1.class);
    }
}
