package com.exchangegw.MultiMessage;

import java.time.LocalTime;
import java.util.HashMap;
import java.util.Map;

import com.exchangegw.HelperUtils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class MessageMM implements MessageFix {
    private String msgType = "MM";  // FIX MsgType

    @JsonProperty("MarketID")
    private String marketID;
    @JsonProperty("Symbol")
    private String symbol;
    @JsonProperty("TransactTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "HHmmssSSS")
    private LocalTime transactTime; 
    @JsonProperty("INAVvalue")
    private Float iNAVvalue;
    // Dữ liệu sở UTC
    @JsonProperty("SendingTime")
    private String sendingTime; //

    public static MessageMM fromMap(Map<String, String> fixFields) {
        Map<String, String> map = new HashMap<>();
        map.put("MarketID", fixFields.get("30001"));
        map.put("Symbol", fixFields.get("55"));
        map.put("TransactTime", fixFields.get("60"));
        map.put("INAVvalue", fixFields.get("30599"));
        map.put("SendingTime", fixFields.get("52"));

        return JsonUtils.MAPPER.convertValue(map, MessageMM.class);
    }
    
}
