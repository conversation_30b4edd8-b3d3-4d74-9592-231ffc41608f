package com.exchangegw.MultiMessage;

import java.util.Map;

import com.exchangegw.core.FixParser;

public interface  MessageFix {
    String getMsgType();

    static MessageFix fromFix(String msgFix) {
        Map<String, String> fixFields = FixParser.parse(msgFix);
        String msgType = fixFields.get("35");
        return switch (msgType) {
            case "d" -> MessageD.fromMap(fixFields);
            case "f" -> MessageF.fromMap(fixFields);
            case "M1" -> MessageM1.fromMap(fixFields);
            case "M2" -> MessageM2.fromMap(fixFields);
            case "M3" -> MessageM3.fromMap(fixFields);
            case "M4" -> MessageM4.fromMap(fixFields);
            case "M7" -> MessageM7.fromMap(fixFields);
            case "M8" -> MessageM8.fromMap(fixFields);
            case "MA" -> MessageMA.fromMap(fixFields);
            case "MD" -> MessageMD.fromMap(fixFields);
            case "ME" -> MessageME.fromMap(fixFields);
            case "MF" -> MessageMF.fromMap(fixFields);
            case "MH" -> MessageMH.fromMap(fixFields);
            case "MI" -> MessageMI.fromMap(fixFields);
            case "ML" -> MessageML.fromMap(fixFields);
            case "MM" -> MessageMM.fromMap(fixFields);
            case "MN" -> MessageMN.fromMap(fixFields);
            case "MO" -> MessageMO.fromMap(fixFields);
            case "MP" -> MessageMP.fromMap(fixFields);
            case "MQ" -> MessageMQ.fromMap(fixFields);
            case "MR" -> MessageMR.fromMap(fixFields);
            case "MS" -> MessageMS.fromMap(fixFields);
            case "MT" -> MessageMT.fromMap(fixFields);
            case "MU" -> MessageMU.fromMap(fixFields);
            case "MV" -> MessageMV.fromMap(fixFields);
            case "MW" -> MessageMW.fromMap(fixFields);
            case "MX" -> MessageMX.fromMap(fixFields);
            case "W" -> MessageW.fromMap(msgFix);
            case "X" -> MessageX.fromMap(msgFix);
            default -> throw new IllegalArgumentException("Unknown MsgType: " + msgType);
        };
    }
}
