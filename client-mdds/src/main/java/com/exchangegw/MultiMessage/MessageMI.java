package com.exchangegw.MultiMessage;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;

import com.exchangegw.HelperUtils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class MessageMI implements MessageFix {
    private String msgType = "MI";  // FIX MsgType

    @JsonProperty("MarketID")
    private String marketID; // Mã thị trường: STO, BDO
    @JsonProperty("Symbol")
    private String symbol;
    @JsonProperty("EventKindCode")
    private String eventKindCode;
    @JsonProperty("EventOccurrenceReasonCode")
    private String eventOccurrenceReasonCode;
    @JsonProperty("EventStartDate")
    @JsonFormat(pattern = "yyyyMMdd")
    private LocalDate eventStartDate;
    @JsonProperty("EventEndDate")
    @JsonFormat(pattern = "yyyyMMdd")
    private LocalDate eventEndDate;
    // Dữ liệu sở UTC
    @JsonProperty("SendingTime")
    private String sendingTime; //

    public static MessageMI fromMap(Map<String, String> fixFields) {
        Map<String, String> map = new HashMap<>();
        map.put("MarketID", fixFields.get("30001"));
        map.put("Symbol", fixFields.get("55"));
        map.put("EventKindCode", fixFields.get("30575"));
        map.put("EventOccurrenceReasonCode", fixFields.get("30576"));
        map.put("EventStartDate", fixFields.get("30577"));
        map.put("EventEndDate", fixFields.get("30578"));

        map.put("SendingTime", fixFields.get("52"));

        return JsonUtils.MAPPER.convertValue(map, MessageMI.class);
    }
}
