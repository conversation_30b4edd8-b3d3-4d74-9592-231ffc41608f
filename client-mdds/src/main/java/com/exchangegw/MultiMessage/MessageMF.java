package com.exchangegw.MultiMessage;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import com.exchangegw.HelperUtils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class MessageMF implements MessageFix {
    private String msgType = "MF";  // FIX MsgType

    @JsonProperty("MarketID")
    private String marketID;
    @JsonProperty("Symbol")
    private String symbol;
    @JsonProperty("ForeignerBuyPosblQty")
    private BigDecimal foreignerBuyPosblQty;

    @JsonProperty("ForeignerOrderLimitQty")
    private BigDecimal foreignerOrderLimitQty;

    // Dữ liệu sở UTC
    @JsonProperty("SendingTime")
    private String sendingTime; //
    
    public static MessageMF fromMap(Map<String, String> fixFields) {
        Map<String, String> map = new HashMap<>();
        map.put("MarketID", fixFields.get("30001"));
        map.put("Symbol", fixFields.get("55"));
        map.put("ForeignerBuyPosblQty", fixFields.get("30557"));
        map.put("ForeignerOrderLimitQty", fixFields.get("30558"));
        map.put("SendingTime", fixFields.get("52"));

        return JsonUtils.MAPPER.convertValue(map, MessageMF.class);
    }
}
