package com.exchangegw.MultiMessage;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import com.exchangegw.HelperUtils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Data;

@Data
public class MessageMD implements MessageFix{
    private String msgType = "MD";  // FIX MsgType

    @JsonProperty("MarketID")
    private String marketID; // String, length 3
    @JsonProperty("BoardID")
    private String boardID; // String, length 2
    @JsonProperty("Symbol")
    private String symbol; // String, length 12
    @JsonProperty("VITypeCode")
    private String viTypeCode; // String, length 1
    @JsonProperty("VIKindCode")
    private String viKindCode; // String, length 1
    @JsonProperty("StaticVIBasePrice")
    private BigDecimal staticVIBasePrice; // Price 15(9.4)
    @JsonProperty("DynamicVIBasePrice")
    private BigDecimal dynamicVIBasePrice; // Price 15(9.4)
    @JsonProperty("VIPrice")
    private BigDecimal viPrice; // Price 15(9.4)
    @JsonProperty("StaticVIDispartiyRatio")
    private BigDecimal staticVIDispartiyRatio; // Price 8(3.3)
    @JsonProperty("DynamicVIDispartiyRatio")
    private BigDecimal dynamicVIDispartiyRatio; // Price 8(3.3)

    // Dữ liệu sở UTC
    @JsonProperty("SendingTime")
    private String sendingTime; //

    public static MessageMD fromMap(Map<String, String> fixFields) {
        Map<String, String> map = new HashMap<>();
        map.put("MarketID", fixFields.get("30001"));
        map.put("BoardID", fixFields.get("20004"));
        map.put("Symbol", fixFields.get("55"));
        map.put("VITypeCode", fixFields.get("20030"));
        map.put("VIKindCode", fixFields.get("20031"));
        map.put("StaticVIBasePrice", fixFields.get("20032"));
        map.put("DynamicVIBasePrice", fixFields.get("20047"));
        map.put("VIPrice", fixFields.get("20033"));
        map.put("StaticVIDispartiyRatio", fixFields.get("20034"));
        map.put("DynamicVIDispartiyRatio", fixFields.get("20048"));

        map.put("SendingTime", fixFields.get("52"));

        return JsonUtils.MAPPER.convertValue(map, MessageMD.class);
    }
}
