package com.exchangegw.MultiMessage;

import java.math.BigDecimal;
import java.time.LocalTime;
import java.util.HashMap;
import java.util.Map;

import com.exchangegw.HelperUtils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class MessageMX implements MessageFix{
    private String msgType = "MX";  // FIX MsgType

    @JsonProperty("MarketID")
    private String marketID;
    @JsonProperty("BoardID")
    private String boardID;  // ID bảng giao dịch: G1, G2
    @JsonProperty("Symbol")
    private String symbol; // Mã ISIN
    @JsonProperty("TransactTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "HHmmssSSS")
    private LocalTime transactTime;  // Tag 60 (UTC time)
    @JsonProperty("PleUpLmtStep")
    private Integer pleUpLmtStep;
    @JsonProperty("PleLwLmtStep")
    private Integer pleLwLmtStep;
    @JsonProperty("HighLimitPrice")
    private BigDecimal highLimitPrice;
    @JsonProperty("LowLimitPrice")
    private BigDecimal lowLimitPrice;

    // Dữ liệu sở UTC
    @JsonProperty("SendingTime")
    private String sendingTime; //

    public static MessageMX fromMap(Map<String, String> fixFields) {
        Map<String, String> map = new HashMap<>();
        map.put("MarketID", fixFields.get("30001"));
        map.put("BoardID", fixFields.get("20004"));
        map.put("Symbol", fixFields.get("55"));
        map.put("TransactTime", fixFields.get("60"));
        map.put("PleUpLmtStep", fixFields.get("30649"));
        map.put("PleLwLmtStep", fixFields.get("30650"));
        map.put("HighLimitPrice", fixFields.get("1149"));
        map.put("LowLimitPrice", fixFields.get("1148"));

        map.put("SendingTime", fixFields.get("52"));

        return JsonUtils.MAPPER.convertValue(map, MessageMX.class);
    }
}
