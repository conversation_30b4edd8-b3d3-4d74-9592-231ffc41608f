package com.exchangegw.MultiMessage;

import java.util.HashMap;
import java.util.Map;

import com.exchangegw.HelperUtils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class MessageMH implements MessageFix {
    private String msgType = "MH";  // FIX MsgType
    
    @JsonProperty("MarketID")
    public String marketID;
    @JsonProperty("MarketMakerContractCode")
    public String marketMakerContractCode;
    @JsonProperty("MemberNo")
    public String memberNo;

    // Dữ liệu sở UTC
    @JsonProperty("SendingTime")
    private String sendingTime; //

    public static MessageMH fromMap(Map<String, String> fixFields) {
        Map<String, String> map = new HashMap<>();
        map.put("MarketID", fixFields.get("30001"));
        map.put("MarketMakerContractCode", fixFields.get("30574"));
        map.put("MemberNo", fixFields.get("20023"));

        map.put("SendingTime", fixFields.get("52"));

        return JsonUtils.MAPPER.convertValue(map, MessageMH.class);
    }
}
