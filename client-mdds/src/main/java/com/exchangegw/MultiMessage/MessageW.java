package com.exchangegw.MultiMessage;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.exchangegw.HelperUtils.HelperUtils;
import com.exchangegw.HelperUtils.JsonUtils;
import com.exchangegw.core.FixParser;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class MessageW implements MessageFix{
    private String msgType = "W";  // FIX MsgType

    @JsonProperty("MarketID")
    private String marketID;            // tag 30001
    @JsonProperty("BoardID")
    private String boardID;             // tag 20004
    @JsonProperty("TradingSessionID")
    private String tradingSessionID;    // tag 336
    @JsonProperty("Symbol")
    private String symbol;              // tag 55
    @JsonProperty("OpnPx")
    private BigDecimal opnPx;           // tag 30561
    @JsonProperty("TrdSessnHighPx")
    private BigDecimal trdSessnHighPx;  // tag 30562
    @JsonProperty("TrdSessnLowPx")
    private BigDecimal trdSessnLowPx;   // tag 30563
    @JsonProperty("SymbolCloseInfoPx")
    private BigDecimal symbolCloseInfoPx; // tag 20026
    @JsonProperty("TotalVolumeTraded")
    private Integer totalVolumeTraded;     // tag 387
    @JsonProperty("GrossTradeAmt")
    private Float grossTradeAmt;   // tag 381 (float)
    @JsonProperty("SellTotOrderQty")
    private Integer sellTotOrderQty;       // tag 30521
    @JsonProperty("BuyTotOrderQty")
    private Integer buyTotOrderQty;        // tag 30522
    @JsonProperty("SellValidOrderCnt")
    private Integer sellValidOrderCnt;  // tag 30523
    @JsonProperty("BuyValidOrderCnt")
    private Integer buyValidOrderCnt;   // tag 30524
    @JsonProperty("NoMDEntries")
    private Integer noMDEntries;        // tag 268
    @JsonProperty("MDEntries")
    private List<MDEntry> mDEntries;    // Repeating Group
    // Dữ liệu sở UTC
    @JsonProperty("SendingTime")
    private String sendingTime; //

    public static class MDEntry {
        @JsonProperty("RptSeq")
        private Integer rptSeq;              // tag 83
        @JsonProperty("MDUpdateAction")
        private String mDUpdateAction;       // tag 279
        @JsonProperty("MDEntryType")
        private String mDEntryType;          // tag 269
        @JsonProperty("MDEntryPositionNo")
        private Integer mDEntryPositionNo;   // tag 290
        @JsonProperty("MDEntryPx")
        private BigDecimal mDEntryPx;        // tag 270
        @JsonProperty("MDEntrySize")
        private Integer mDEntrySize;            // tag 271
        @JsonProperty("NumberOfOrders")
        private Integer mDmberOfOrders;      // tag 346
        @JsonProperty("MDEntryMMSize")
        private Integer mDEntryMMSize;          // tag 30271
    }

    public static MessageW fromMap(String msgFix) {
        Map<String, String> fixFields = FixParser.parse(msgFix);
        Map<String, String> map = new HashMap<>();
        map.put("MarketID", fixFields.get("30001"));        
        map.put("BoardID", fixFields.get("20004"));        
        map.put("TradingSessionID", fixFields.get("336"));        
        map.put("Symbol", fixFields.get("55"));        
        map.put("OpnPx", fixFields.get("30561"));        
        map.put("TrdSessnHighPx", fixFields.get("30562"));        
        map.put("TrdSessnLowPx", fixFields.get("30563"));        
        map.put("SymbolCloseInfoPx", fixFields.get("20026"));        
        map.put("TotalVolumeTraded", fixFields.get("387"));        
        map.put("GrossTradeAmt", fixFields.get("381"));        
        map.put("SellTotOrderQty", fixFields.get("30521"));        
        map.put("BuyTotOrderQty", fixFields.get("30522"));        
        map.put("SellValidOrderCnt", fixFields.get("30523"));        
        map.put("BuyValidOrderCnt", fixFields.get("30524"));        
        map.put("NoMDEntries", fixFields.get("268"));
        map.put("SendingTime", fixFields.get("52"));

        MessageW msgW = JsonUtils.MAPPER.convertValue(map, MessageW.class);
         // Convert repeating group
         // Convert repeating group
        List<MessageW.MDEntry> entries = new ArrayList<>();
        List<Map<String, String>> repeatingGroups = HelperUtils.parseRepeatingGroups(msgFix);
        for (Map<String, String> entryMap : repeatingGroups) {
            Map<String, String> mapEntryMap = new HashMap<>();
            mapEntryMap.put("RptSeq", entryMap.get("83")); 
            mapEntryMap.put("MDUpdateAction", entryMap.get("279"));
            mapEntryMap.put("MDEntryType", entryMap.get("269"));
            mapEntryMap.put("MDEntryPositionNo", entryMap.get("290"));
            mapEntryMap.put("MDEntryPx", entryMap.get("270"));
            mapEntryMap.put("MDEntrySize", entryMap.get("271"));
            mapEntryMap.put("NumberOfOrders", entryMap.get("346")); 
            mapEntryMap.put("MDEntryMMSize", entryMap.get("30271")); 

            MessageW.MDEntry entry = JsonUtils.MAPPER.convertValue(mapEntryMap, MessageW.MDEntry.class);
            entries.add(entry);
        }
        msgW.setMDEntries(entries);
        return msgW;
    }
}
