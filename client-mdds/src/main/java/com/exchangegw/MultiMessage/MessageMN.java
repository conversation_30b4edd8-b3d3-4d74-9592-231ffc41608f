package com.exchangegw.MultiMessage;

import java.time.LocalTime;
import java.util.HashMap;
import java.util.Map;

import com.exchangegw.HelperUtils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class MessageMN implements MessageFix {
    private String msgType = "MN";  // FIX MsgType

    @JsonProperty("MarketID")
    private String marketID;
    @JsonProperty("Symbol")
    private String symbol;
    @JsonProperty("TransactTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "HHmmssSSS")
    private LocalTime transactTime;  // Tag 60 (UTC time)
    @JsonProperty("ValueIndexes")
    private Float valueIndexes;
    // Dữ liệu sở UTC
    @JsonProperty("SendingTime")
    private String sendingTime; //

    public static MessageMN fromMap(Map<String, String> fixFields) {
        Map<String, String> map = new HashMap<>();
        map.put("MarketID", fixFields.get("30001"));
        map.put("Symbol", fixFields.get("55"));
        map.put("TransactTime", fixFields.get("60"));
        map.put("ValueIndexes", fixFields.get("30217"));
        map.put("SendingTime", fixFields.get("52"));

        return JsonUtils.MAPPER.convertValue(map, MessageMN.class);
    }
}
