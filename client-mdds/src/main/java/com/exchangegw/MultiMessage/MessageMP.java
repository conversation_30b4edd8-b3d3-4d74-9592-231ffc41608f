package com.exchangegw.MultiMessage;

import java.util.HashMap;
import java.util.Map;

import com.exchangegw.HelperUtils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;


@Data
public class MessageMP implements MessageFix {
    private String msgType = "MP";  // FIX MsgType
    
    @JsonProperty("MarketID")
    private String marketID;
    @JsonProperty("TopNumReports")
    private Integer topNumReports;
    @JsonProperty("Rank")
    private Integer rank;
    @JsonProperty("Symbol")
    private String symbol;
    @JsonProperty("MDEntrySize")
    private Integer mDEntrySize;
    // Dữ liệu sở UTC
    @JsonProperty("SendingTime")
    private String sendingTime; //

    public static MessageMP fromMap(Map<String, String> fixFields) {
        Map<String, String> map = new HashMap<>();
        map.put("MarketID", fixFields.get("30001"));
        map.put("TopNumReports", fixFields.get("911"));
        map.put("Rank", fixFields.get("30634"));
        map.put("Symbol", fixFields.get("55"));
        map.put("MDEntrySize", fixFields.get("271"));
        map.put("SendingTime", fixFields.get("52"));

        return JsonUtils.MAPPER.convertValue(map, MessageMP.class);
    }
}
