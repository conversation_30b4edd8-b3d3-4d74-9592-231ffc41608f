package com.exchangegw.MultiMessage;

import java.util.HashMap;
import java.util.Map;

import com.exchangegw.HelperUtils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class MessageML implements MessageFix{
    private String msgType = "ML";  // FIX MsgType

    @JsonProperty("MarketID")
    private String marketID; // Mã thị trường: STO, BDO
    @JsonProperty("MarketIndexClass")
    private String marketIndexClass;
    @JsonProperty("IndexsTypeCode")
    private String indexsTypeCode;
    @JsonProperty("Currency")
    private String currency;
    @JsonProperty("IdxName")
    private String idxName;
    @JsonProperty("IdxEnglishName")
    private String idxEnglishName;
    @JsonProperty("TotalMsgNo")
    private Integer totalMsgNo;
    @JsonProperty("CurrentMsgNo")
    private Integer currentMsgNo;
    @JsonProperty("Symbol")
    private String symbol;
    // Dữ liệu sở UTC
    @JsonProperty("SendingTime")
    private String sendingTime; //

    public static MessageML fromMap(Map<String, String> fixFields) {
        Map<String, String> map = new HashMap<>();
        map.put("MarketID", fixFields.get("30001"));
        map.put("MarketIndexClass", fixFields.get("30569"));
        map.put("IndexsTypeCode", fixFields.get("30167"));
        map.put("Currency", fixFields.get("15"));
        map.put("IdxName", fixFields.get("30632"));
        map.put("IdxEnglishName", fixFields.get("30633"));
        map.put("TotalMsgNo", fixFields.get("30606"));
        map.put("CurrentMsgNo", fixFields.get("30607"));
        map.put("Symbol", fixFields.get("55"));

        map.put("SendingTime", fixFields.get("52"));

        return JsonUtils.MAPPER.convertValue(map, MessageML.class);
    }
}
