package com.exchangegw.MultiMessage;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;

import com.exchangegw.HelperUtils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class MessageMO implements MessageFix {
    private String msgType = "MO";  // FIX MsgType

    @JsonProperty("MarketID")
    private String marketID;
    @JsonProperty("Symbol")
    private String symbol;
    @JsonProperty("TradeDate")
    @JsonFormat(pattern = "yyyyMMdd")
    private LocalDate tradeDate;
    @JsonProperty("TrackingError")
    private Float trackingError;
    @JsonProperty("DisparateRatio")
    private Float disparateRatio;
    // Dữ liệu sở UTC
    @JsonProperty("SendingTime")
    private String sendingTime; //

    public static MessageMO fromMap(Map<String, String> fixFields) {
        Map<String, String> map = new HashMap<>();
        map.put("MarketID", fixFields.get("30001"));
        map.put("Symbol", fixFields.get("55"));
        map.put("TradeDate", fixFields.get("75"));
        map.put("TrackingError", fixFields.get("30600"));
        map.put("DisparateRatio", fixFields.get("30602"));
        map.put("SendingTime", fixFields.get("52"));

        return JsonUtils.MAPPER.convertValue(map, MessageMO.class);
    }

}
