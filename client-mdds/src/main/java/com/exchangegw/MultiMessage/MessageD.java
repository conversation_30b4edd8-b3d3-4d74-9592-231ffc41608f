package com.exchangegw.MultiMessage;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;

import com.exchangegw.HelperUtils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class MessageD implements MessageFix { //d = Security Definition 
    private String msgType = "D";  // FIX MsgType
    
    @JsonProperty("MarketID")
    private String marketID; // Mã thị trường: STO, BDO

    @JsonProperty("BoardID")
    private String boardID;  // ID bảng giao dịch: G1, G2

    @JsonProperty("TotNoRelatedSym") 
    private Integer totNumReports;  // Tổng số message

    @JsonProperty("SecurityExchange")
    private String securityExchange; // Sàn giao dịch: HO, HX

    @JsonProperty("Symbol")
    private String symbol; // Mã ISIN
    @JsonProperty("TickerCode")
    private String tickerCode; // Mã chứng khoán
    @JsonProperty("SymbolShortCode")
    private String symbolShortCode;  // Mã chứng khoán ngắn gọn
    @JsonProperty("SymbolName")
    private String symbolName;  // Tên chứng khoán
    @JsonProperty("SymbolEnglishName")
    private String symbolEnglishName;  // Tên tiếng Anh
    @JsonProperty("ProductID")
    private String productID;   // Mã sản phẩm
    @JsonProperty("ProductGroupID")
    private String productGrpID;   // Nhóm sản phẩm
    @JsonProperty("SecurityGroupID")
    private String securityGroupID;   // Nhóm chứng khoán
    @JsonProperty("PutOrCall")
    private String putOrCall; // Quyền chọn: P, C
    @JsonProperty("ExerciseStyle")
    private String exerciseStyle; // Kiểu thực hiện: A, E, B, Z
    @JsonProperty("MaturityMonthYear")
    private String maturityMonthYear; // Năm tháng đáo hạn: YYYYMM
    @JsonProperty("MaturityDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate maturityDate;  // Ngày đáo hạn
    @JsonProperty("Issuer")
    private String issuer;  // Mã tổ chức phát hành
    @JsonProperty("IssueDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate issueDate;  // Ngày phát hành
    @JsonProperty("ContractMultiplier")
    private Double contractMultiplier;  // Hệ số nhân hợp đồng
    @JsonProperty("CouponRate")
    private Double couponRate;  // Lãi suất trái phiếu
    @JsonProperty("Currency")
    private String currency;  // Loại tiền tệ: VND, USD
    @JsonProperty("ListedShares")
    private Integer listedShares;  // Khối lượng cổ phiếu niêm yết
    @JsonProperty("HighLimitPrice")
    private BigDecimal highLimitPrice;  // Giá trần
    @JsonProperty("LowLimitPrice")
    private BigDecimal lowLimitPrice;  // Giá sàn
    @JsonProperty("StrikePrice")
    private Double strikePrice;  // Giá thực hiện
    @JsonProperty("SecurityStatus")
    private String securityStatus;  // Trạng thái: Y, N
    @JsonProperty("ContractSize")
    private Double contractSize;  // Đơn vị giao dịch tối thiểu
    @JsonProperty("SettlMethod")
    private String settlMethod;  // Phương thức thanh toán: C, D, A, O
    @JsonProperty("Yield")
    private Double yield;  // Lợi suất
    @JsonProperty("ReferencePrice")
    private BigDecimal referencePrice;  // Giá tham chiếu
    @JsonProperty("EvaluationPrice")
    private Double evaluationPrice;  // Giá định giá
    @JsonProperty("HgstOrderPrice")
    private Double hgstOrderPrice;  // Giá đặt cao nhất
    @JsonProperty("LwstOrderPrice")
    private Double lwstOrderPrice;  // Giá đặt thấp nhất
    @JsonProperty("PrevClosePx")
    private Double prevClosePx;  // Giá đóng cửa phiên trước
    @JsonProperty("SymbolCloseInfoPxType")
    private String symbolCloseInfoPxType;  // Phương thức xác định giá đóng cửa: 1,2,3,4
    @JsonProperty("FirstTradingDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate firstTradingDate;  // Ngày giao dịch đầu tiên
    @JsonProperty("FinalTradeDate")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDate finalTradeDate;  // Ngày giao dịch cuối cùng
    @JsonProperty("FinalSettleDate")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyyMMdd")
    private LocalDate finalSettleDate;  // Ngày thanh toán cuối cùng
    @JsonProperty("ListingDate")
    @JsonFormat(pattern = "yyyyMMdd")
    private LocalDate listingDate; // Ngày niêm yết
    @JsonProperty("OpenInterestQty")
    private Integer openInterestQty;  // Khối lượng mở (open interest)

    @JsonProperty("SettlPrice")
    private Double settlementPrice;  // Giá thanh toán

    @JsonProperty("RandomEndTriggeringConditionCode")
    private String RandomEndTriggeringConditionCode;  //  kết thúc ngẫu nhiên vào     cuối mỗi phiên giao dịch

    @JsonProperty("ExClassType")
    private String ExClassType;  //  Giao dịch không hưởng quyền

    @JsonProperty("VWAP")
    private BigDecimal VWAP;  //  Giá bình quân gia quyền

    @JsonProperty("SymbolAdminStatusCode")
    private String SymbolAdminStatusCode;  //  tình trạng quản lý mã chứng khoán khi vi phạm

    @JsonProperty("SymbolTradingMethodStatusCode")
    private String SymbolTradingMethodStatusCode;  // Xác định loại giao dịch cho một mã chứng khoán

    @JsonProperty("SectorTypeCode")
    private String SectorTypeCode;  //  phân loại tình trạng giao dịch như tạm ngưng,

    @JsonProperty("RedumptionDate")
    private String RedumptionDate;


    // Dữ liệu sở UTC
    @JsonProperty("SendingTime")
    private String sendingTime; //

    public static MessageD fromMap(Map<String, String> fixFields) {
        Map<String, String> map = new HashMap<>();
        map.put("MarketID", fixFields.get("30001"));
        map.put("BoardID", fixFields.get("20004"));
        map.put("SecurityExchange", fixFields.get("207"));
        map.put("Symbol", fixFields.get("55"));
        map.put("TickerCode", fixFields.get("30624"));
        map.put("SymbolShortCode", fixFields.get("30628"));
        map.put("SymbolName", fixFields.get("30629"));
        map.put("SymbolEnglishName", fixFields.get("30630"));
        map.put("ProductID", fixFields.get("20009"));
        map.put("ProductGroupID", fixFields.get("20003"));
        map.put("SecurityGroupID", fixFields.get("30604"));
        map.put("Issuer", fixFields.get("106"));
        map.put("PutOrCall", fixFields.get("201"));
        map.put("ExerciseStyle", fixFields.get("1194"));
        map.put("MaturityMonthYear", fixFields.get("200"));
        map.put("MaturityDate", fixFields.get("541"));
        map.put("TotNumReports", fixFields.get("911"));
        map.put("IssueDate", fixFields.get("225"));
        map.put("ContractMultiplier", fixFields.get("231"));
        map.put("CouponRate", fixFields.get("223"));
        map.put("Currency", fixFields.get("15"));
        map.put("ListedShares", fixFields.get("20020"));
        map.put("HighLimitPrice", fixFields.get("1149"));
        map.put("LowLimitPrice", fixFields.get("1148"));
        map.put("StrikePrice", fixFields.get("202"));
        map.put("SecurityStatus", fixFields.get("965"));
        map.put("ContractSize", fixFields.get("30631"));
        map.put("SettlMethod", fixFields.get("1193"));
        map.put("Yield", fixFields.get("236"));
        map.put("ReferencePrice", fixFields.get("20013"));
        map.put("EvaluationPrice", fixFields.get("20014"));
        map.put("HgstOrderPrice", fixFields.get("20015"));
        map.put("LwstOrderPrice", fixFields.get("20016"));
        map.put("PrevClosePx", fixFields.get("140"));
        map.put("SymbolCloseInfoPxType", fixFields.get("20027"));
        map.put("FirstTradingDate", fixFields.get("30642"));
        map.put("FinalTradeDate", fixFields.get("30511"));
        map.put("FinalSettleDate", fixFields.get("30512"));
        map.put("ListingDate", fixFields.get("30301"));
        map.put("OpenInterestQty", fixFields.get("30540"));
        map.put("SettlementPrice", fixFields.get("30573"));
        map.put("RandomEndTriggeringConditionCode", fixFields.get("30614"));
        map.put("ExClassType", fixFields.get("20018"));
        map.put("VWAP", fixFields.get("30625"));
        map.put("SymbolAdminStatusCode", fixFields.get("30635"));
        map.put("SymbolTradingMethodStatusCode", fixFields.get("30636"));
        map.put("SymbolTradingSantionStatusCode", fixFields.get("30637"));
        map.put("SectorTypeCode", fixFields.get("30647"));
        map.put("RedumptionDate", fixFields.get("30652"));

        map.put("SendingTime", fixFields.get("52"));

        // Format date yyyyMMdd -> yyyy-MM-dd
        String date = fixFields.get("30301");
        if (date != null && date.length() == 8) {
            map.put("FirstTradingDate", date.substring(0, 4) + "-" + date.substring(4, 6) + "-" + date.substring(6, 8));
        }

        return JsonUtils.MAPPER.convertValue(map, MessageD.class);
    }

}
