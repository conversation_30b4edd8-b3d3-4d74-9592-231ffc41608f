package com.exchangegw.MultiMessage;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import com.exchangegw.HelperUtils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class MessageM7 implements MessageFix {
    private String msgType = "M7";

    @JsonProperty("MarketID")
    private String marketID;
    @JsonProperty("BoardID")
    private String boardID;
    @JsonProperty("Symbol")
    private String symbol;
    @JsonProperty("ReferencePrice")
    private BigDecimal referencePrice;
    @JsonProperty("HighLimitPrice")
    private BigDecimal highLimitPrice;
    @JsonProperty("LowLimitPrice")
    private BigDecimal lowLimitPrice;
    @JsonProperty("EvaluationPrice")
    private BigDecimal evaluationPrice;
    @JsonProperty("HgstOrderPrice")
    private BigDecimal highestOrderPrice;
    @JsonProperty("LwstOrderPrice")
    private BigDecimal lowestOrderPrice;
    @JsonProperty("ListedShares")
    private Integer listedShares;
    @JsonProperty("ExClassType")
    private String exClassType;

    // Dữ liệu sở UTC
    @JsonProperty("SendingTime")
    private String sendingTime; //


    public static MessageM7 fromMap(Map<String, String> fixFields) {
        Map<String, String> map = new HashMap<>();
        map.put("MarketID", fixFields.get("30001"));
        map.put("BoardID", fixFields.get("20004"));
        map.put("Symbol", fixFields.get("55"));
        map.put("ReferencePrice", fixFields.get("20013"));
        map.put("HighLimitPrice", fixFields.get("1149"));
        map.put("LowLimitPrice", fixFields.get("1148"));
        map.put("EvaluationPrice", fixFields.get("20014"));
        map.put("HgstOrderPrice", fixFields.get("20015"));
        map.put("LwstOrderPrice", fixFields.get("20016"));
        map.put("ListedShares", fixFields.get("20020"));
        map.put("ExClassType", fixFields.get("20018"));

        map.put("SendingTime", fixFields.get("52"));
        return JsonUtils.MAPPER.convertValue(map, MessageM7.class);
    }
}
