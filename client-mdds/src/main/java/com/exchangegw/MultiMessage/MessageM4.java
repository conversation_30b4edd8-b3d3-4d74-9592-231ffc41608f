package com.exchangegw.MultiMessage;

import java.util.HashMap;
import java.util.Map;

import com.exchangegw.HelperUtils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class MessageM4 implements MessageFix{
    private String msgType = "M4";  // FIX MsgType

    @JsonProperty("MarketID")
    private String marketID;  
    @JsonProperty("Symbol")
    private String symbol;
    @JsonProperty("TotNumReports")
    private Integer totNumReports;
    @JsonProperty("SellRankSeq")
    private Integer sellRankSeq;
    @JsonProperty("SellMemberNo")
    private String sellMemberNo;
    @JsonProperty("SellVolume")
    private Integer sellVolume;
    @JsonProperty("SellTradeAmount")
    private Float SellTradeAmount;
    @JsonProperty("BuyRankSeq")
    private Integer buyRankSeq;
    @JsonProperty("BuyMemberNo")
    private String buyMemberNo;
    @JsonProperty("BuyVolume")
    private Integer buyVolume;
    @JsonProperty("BuyTradedAmount")
    private Float BuyTradedAmount;

    // Dữ liệu sở UTC
    @JsonProperty("SendingTime")
    private String sendingTime; //

    public static MessageM4 fromMap(Map<String, String> fixFields) {
        Map<String, String> map = new HashMap<>();
        map.put("MarketID", fixFields.get("30001"));
        map.put("Symbol", fixFields.get("55"));
        map.put("TotNumReports", fixFields.get("911"));
        map.put("SellRankSeq", fixFields.get("30215"));
        map.put("SellMemberNo", fixFields.get("30213"));
        map.put("SellVolume", fixFields.get("331"));
        map.put("SellTradeAmount", fixFields.get("30168"));
        map.put("BuyRankSeq", fixFields.get("30216"));
        map.put("BuyMemberNo", fixFields.get("30214"));
        map.put("BuyVolume", fixFields.get("330"));
        map.put("BuyTradedAmount", fixFields.get("30169"));

        map.put("SendingTime", fixFields.get("52"));

        return JsonUtils.MAPPER.convertValue(map, MessageM4.class);
    }
}
