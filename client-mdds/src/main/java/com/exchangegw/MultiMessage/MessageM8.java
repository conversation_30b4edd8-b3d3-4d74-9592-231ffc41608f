package com.exchangegw.MultiMessage;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import com.exchangegw.HelperUtils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class MessageM8 implements MessageFix {
    private String msgType = "M8";  // FIX MsgType

    @JsonProperty("MarketID")
    private String marketID; // String, length 3
    @JsonProperty("BoardID")
    private String boardID; // String, length 2
    @JsonProperty("Symbol")
    private String symbol; // String, length 12
    @JsonProperty("SymbolCloseInfoPx")
    private BigDecimal symbolCloseInfoPx; // Price 15(9.4)
    @JsonProperty("SymbolCloseInfoYield")
    private Float symbolCloseInfoYield; // Float 13(5.6)
    @JsonProperty("SymbolCloseInfoPxType")
    private String symbolCloseInfoPxType; // String, length 1

    // Dữ liệu sở UTC
    @JsonProperty("SendingTime")
    private String sendingTime; //

    public static MessageM7 fromMap(Map<String, String> fixFields) {
        Map<String, String> map = new HashMap<>();
        map.put("MarketID", fixFields.get("30001"));
        map.put("BoardID", fixFields.get("20004"));
        map.put("Symbol", fixFields.get("55"));
        map.put("SymbolCloseInfoPx", fixFields.get("20026"));
        map.put("SymbolCloseInfoYield", fixFields.get("30541"));
        map.put("SymbolCloseInfoPxType", fixFields.get("20027"));

        map.put("SendingTime", fixFields.get("52"));

        return JsonUtils.MAPPER.convertValue(map, MessageM7.class);
    }
}
