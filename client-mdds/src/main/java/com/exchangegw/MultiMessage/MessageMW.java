package com.exchangegw.MultiMessage;


import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import com.exchangegw.HelperUtils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class MessageMW implements MessageFix{
    private final String msgType = "MW";

    @JsonProperty("MarketID")
    private String marketID;
    @JsonProperty("BoardID")
    private String boardID;
    @JsonProperty("Symbol")
    private String symbol;
    @JsonProperty("TransactTime")
    private String transactTime; // ISO 8601 String (e.g., "2025-05-28T09:30:00Z")
    @JsonProperty("RandomEndApplyClassification")
    private String randomEndApplyClassification;
    @JsonProperty("RandomEndTentativExecutionPrice")
    private BigDecimal randomEndTentativExecutionPrice;
    @JsonProperty("RandomEndEstimatedHighestPrice")
    private BigDecimal randomEndEstimatedHighestPrice;
    @JsonProperty("RandomEndEstimatedHighestPriceDisparateRatio")
    private Float randomEndEstimatedHighestPriceDisparateRatio;
    @JsonProperty("RandomEndEstimatedLowestPrice")
    private BigDecimal randomEndEstimatedLowestPrice;
    @JsonProperty("RandomEndEstimatedLowestPriceDisparateRatio")
    private Float randomEndEstimatedLowestPriceDisparateRatio;
    @JsonProperty("LatestPrice")
    private BigDecimal latestPrice;
    @JsonProperty("LatestPriceDisparateRatio")
    private Float latestPriceDisparateRatio;
    @JsonProperty("RandomEndReleaseTime")
    private String randomEndReleaseTime;

    // Dữ liệu sở UTC
    @JsonProperty("SendingTime")
    private String sendingTime; //

    public static MessageMW fromMap(Map<String, String> fixFields) {
        Map<String, String> map = new HashMap<>();
        map.put("MarketID", fixFields.get("30001"));
        map.put("BoardID", fixFields.get("20004"));
        map.put("Symbol", fixFields.get("55"));
        map.put("TransactTime", fixFields.get("60"));
        map.put("RandomEndApplyClassification", fixFields.get("30615"));
        map.put("RandomEndTentativExecutionPrice", fixFields.get("30616"));
        map.put("RandomEndEstimatedHighestPrice", fixFields.get("30617"));
        map.put("RandomEndEstimatedHighestPriceDisparateRatio", fixFields.get("30618"));
        map.put("RandomEndEstimatedLowestPrice", fixFields.get("30619"));
        map.put("RandomEndEstimatedLowestPriceDisparateRatio", fixFields.get("30620"));
        map.put("LatestPrice", fixFields.get("30621"));
        map.put("LatestPriceDisparateRatio", fixFields.get("30622"));
        map.put("RandomEndReleaseTime", fixFields.get("30623"));
        map.put("SendingTime", fixFields.get("52"));

        return JsonUtils.MAPPER.convertValue(map, MessageMW.class);
    }
}
