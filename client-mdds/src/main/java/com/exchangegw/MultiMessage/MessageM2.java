package com.exchangegw.MultiMessage;

import java.time.LocalTime;
import java.util.HashMap;
import java.util.Map;

import com.exchangegw.HelperUtils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class MessageM2 implements MessageFix {
    private String msgType = "M2";  // FIX MsgType

    @JsonProperty("MarketID")
    private String marketID;             // Tag 30001 - String (length 3)
    @JsonProperty("TransactTime")
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "HHmmssSSS")
    private LocalTime transactTime;         // Tag 30569 - UTCTime (ISO 8601)
    @JsonProperty("MarketIndexClass")
    private String MarketIndexClass;             // Tag 30597 - String (length 10)
    @JsonProperty("BondClassificationCode")
    private String BondClassificationCode;             // Tag 30597 - String (length 10)
    @JsonProperty("SecurityGroupID")
    private String SecurityGroupID;             // Tag 30604 - String (length 2)
    @JsonProperty("InvestCode")
    private String investCode;           // Tag 20000 - String (length 4)
    @JsonProperty("SellVolume")
    private Integer sellVolume;              // Tag 331 - Int (length 12)
    @JsonProperty("SellTradeAmount")
    private Float sellTradeAmount;      // Tag 30168 - Float (23 digits, 4 decimal places)
    @JsonProperty("BuyVolume")
    private Integer buyVolume;               // Tag 330 - Int (length 12)
    @JsonProperty("BuyTradedAmount")
    private Float buyTradedAmount;      // Tag 30169 - Float (23 digits, 4 decimal places)
    // Dữ liệu sở UTC
    @JsonProperty("SendingTime")
    private String sendingTime; //

     public static MessageM2 fromMap(Map<String, String> fixFields) {
        Map<String, String> map = new HashMap<>();
        map.put("MarketID", fixFields.get("30001"));
        map.put("TransactTime", fixFields.get("60"));
        map.put("MarketIndexClass", fixFields.get("30569"));
        map.put("BondClassificationCode", fixFields.get("30597"));
        map.put("SecurityGroupID", fixFields.get("30604"));
        map.put("InvestCode", fixFields.get("20000"));
        map.put("SellVolume", fixFields.get("331"));
        map.put("SellTradeAmount", fixFields.get("30168"));
        map.put("BuyVolume", fixFields.get("330"));
        map.put("BuyTradedAmount", fixFields.get("30169"));

        map.put("SendingTime", fixFields.get("52"));

        return JsonUtils.MAPPER.convertValue(map, MessageM2.class);
    }
}
