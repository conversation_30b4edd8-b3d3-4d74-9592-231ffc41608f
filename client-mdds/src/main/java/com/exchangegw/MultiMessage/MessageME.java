package com.exchangegw.MultiMessage;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import com.exchangegw.HelperUtils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class MessageME implements MessageFix {
    private String msgType = "ME";  // FIX MsgType

    @JsonProperty("MarketID")
    private String marketID;
    @JsonProperty("BoardID")
    private String boardID;
    @JsonProperty("Symbol")
    private String symbol;
    @JsonProperty("ExpectedTradePx")
    private BigDecimal expectedTradePx;
    @JsonProperty("ExpectedTradeQty")
    private Integer expectedTradeQty;
    @JsonProperty("ExpectedTradeYield")
    private Float expectedTradeYield;

    // Dữ liệu sở UTC
    @JsonProperty("SendingTime")
    private String sendingTime; //

    public static MessageME fromMap(Map<String, String> fixFields) {
        Map<String, String> map = new HashMap<>();
        map.put("MarketID", fixFields.get("30573"));        
        map.put("BoardID", fixFields.get("30573"));        
        map.put("Symbol", fixFields.get("30573"));        
        map.put("ExpectedTradePx", fixFields.get("30573"));        
        map.put("ExpectedTradeQty", fixFields.get("30573"));        
        map.put("ExpectedTradeYield", fixFields.get("30573"));

        map.put("SendingTime", fixFields.get("52"));

        return JsonUtils.MAPPER.convertValue(map, MessageME.class);
    }
}
