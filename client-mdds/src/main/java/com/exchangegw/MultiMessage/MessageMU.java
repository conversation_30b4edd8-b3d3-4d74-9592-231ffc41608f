package com.exchangegw.MultiMessage;

import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;

import com.exchangegw.HelperUtils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class MessageMU implements MessageFix{
    private String msgType = "MU";  // FIX MsgType

    @JsonProperty("MarketID")
    private String marketID;
    @JsonProperty("SecurityExchange")
    private String securityExchange;
    @JsonProperty("Symbol")
    private String symbol;
    @JsonProperty("SymbolName")
    private String symbolName;
    @JsonProperty("DisclosureID")
    private String disclosureID;
    @JsonProperty("TotalMsgNo")
    private Integer totalMsgNo;
    @JsonProperty("CurrentMsgNo")
    private Integer currentMsgNo;
    @JsonProperty("LanguageCategory")
    private String languageCategory;
    @JsonProperty("DataCategory")
    private String dataCategory;
    @JsonProperty("PublicInformationDate")
    @JsonFormat(pattern = "yyyyMMdd")
    private LocalDate publicInformationDate;
    @JsonProperty("TransmissionDate")
    @JsonFormat(pattern = "yyyyMMdd")
    private LocalDate transmissionDate;
    @JsonProperty("ProcessType")
    private String processType;
    @JsonProperty("Headline")
    private String headline;
    @JsonProperty("Body")
    private String body;

    // Dữ liệu sở UTC
    @JsonProperty("SendingTime")
    private String sendingTime;

    public static MessageMU fromMap(Map<String, String> fixFields) {
        Map<String, String> map = new HashMap<>();
        map.put("MarketID", fixFields.get("30001"));
        map.put("SecurityExchange", fixFields.get("207"));
        map.put("Symbol", fixFields.get("55"));
        map.put("SymbolName", fixFields.get("30629"));
        map.put("DisclosureID", fixFields.get("30605"));
        map.put("TotalMsgNo", fixFields.get("30606"));
        map.put("CurrentMsgNo", fixFields.get("30607"));
        map.put("LanguageCategory", fixFields.get("30608"));
        map.put("DataCategory", fixFields.get("30609"));
        map.put("PublicInformationDate", fixFields.get("30610"));
        map.put("TransmissionDate", fixFields.get("30611"));
        map.put("ProcessType", fixFields.get("30612"));
        map.put("Headline", fixFields.get("148"));
        map.put("Body", fixFields.get("30613"));
        map.put("SendingTime", fixFields.get("52"));

        return JsonUtils.MAPPER.convertValue(map, MessageMU.class);
    }
    
}
