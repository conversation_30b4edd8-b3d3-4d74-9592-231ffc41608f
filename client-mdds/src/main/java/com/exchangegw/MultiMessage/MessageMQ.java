package com.exchangegw.MultiMessage;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import com.exchangegw.HelperUtils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;


@Data
public class MessageMQ implements MessageFix{
    private String msgType = "MQ";  // FIX MsgType

    @JsonProperty("MarketID")
    private String marketID;
    @JsonProperty("TotNumReports")
    private Integer totNumReports;
    @JsonProperty("Rank")
    private Integer rank;
    @JsonProperty("Symbol")
    private String symbol;
    @JsonProperty("MDEntryPx")
    private BigDecimal mDEntryPx;
    // Dữ liệu sở UTC
    @JsonProperty("SendingTime")
    private String sendingTime; //

    public static MessageMQ fromMap(Map<String, String> fixFields) {
        Map<String, String> map = new HashMap<>();
        map.put("MarketID", fixFields.get("30001"));
        map.put("TotNumReports", fixFields.get("911"));
        map.put("Rank", fixFields.get("30634"));
        map.put("Symbol", fixFields.get("55"));
        map.put("MDEntryPx", fixFields.get("270"));
        map.put("SendingTime", fixFields.get("52"));

        return JsonUtils.MAPPER.convertValue(map, MessageMQ.class);
    }
      
}
