package com.exchangegw.MultiMessage;

import java.util.HashMap;
import java.util.Map;

import com.exchangegw.HelperUtils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class MessageM3 implements MessageFix {
    private String msgType = "M3";  // FIX MsgType

    @JsonProperty("MarketID")
    private String marketID;             // Tag 30001 - String (length 3)
    @JsonProperty("Symbol")
    private String symbol;
    @JsonProperty("InvestCode")
    private String investCode;
    @JsonProperty("SellVolume")
    private Integer sellVolume;
    @JsonProperty("SellTradeAmount")
    private Float sellTradeAmount;
    @JsonProperty("BuyVolumn")
    private Integer buyVolumn;
    @JsonProperty("BuyTradedAmount")
    private Float buyTradedAmount;

    // Dữ liệu sở UTC
    @JsonProperty("SendingTime")
    private String sendingTime; //

    public static MessageM3 fromMap(Map<String, String> fixFields) {
        Map<String, String> map = new HashMap<>();
        map.put("MarketID", fixFields.get("30001"));
        map.put("Symbol", fixFields.get("55"));
        map.put("InvestCode", fixFields.get("20000"));
        map.put("SellVolume", fixFields.get("331"));
        map.put("SellTradeAmount", fixFields.get("30168"));
        map.put("BuyVolumn", fixFields.get("330"));
        map.put("BuyTradedAmount", fixFields.get("30169"));
        map.put("SendingTime", fixFields.get("52"));

        return JsonUtils.MAPPER.convertValue(map, MessageM3.class);
    }
}
