package com.exchangegw.MultiMessage;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashMap;
import java.util.Map;

import com.exchangegw.HelperUtils.JsonUtils;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

@Data
public class MessageMA implements MessageFix{
    private String msgType = "MA";  // FIX MsgType

    @JsonProperty("MarketID")
    private String marketID;
    @JsonProperty("Symbol")
    private String symbol;
    @JsonProperty("TradeDate")
    @JsonFormat(pattern = "yyyyMMdd")
    private LocalDate tradeDate;
    @JsonProperty("OpenInterestQty")
    private Integer openInterestQty;
    @JsonProperty("SettlementPrice")
    private BigDecimal settlementPrice;

    // Dữ liệu sở UTC
    @JsonProperty("SendingTime")
    private String sendingTime; //

    public static MessageMA fromMap(Map<String, String> fixFields) {
        Map<String, String> map = new HashMap<>();
        map.put("MarketID", fixFields.get("30001"));
        map.put("Symbol", fixFields.get("55"));
        map.put("TradeDate", fixFields.get("75"));
        map.put("OpenInterestQty", fixFields.get("30540"));
        map.put("SettlementPrice", fixFields.get("30573"));

        map.put("SendingTime", fixFields.get("52"));

        return JsonUtils.MAPPER.convertValue(map, MessageMA.class);
    }
}
