package com.exchangegw.publisher;

import static com.exchangegw.HelperUtils.HelperUtils.*;

import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import com.exchangegw.handler.ExchangeHandlerFactory;
import com.exchangegw.handler.ExchangeMessageHandler;
import com.exchangegw.model.IndexInfo;
import com.exchangegw.model.PublishEvent;
import com.exchangegw.model.StockCode;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class MultiZmqPublisher {
    private static final Logger logger = LoggerFactory.getLogger(MultiZmqPublisher.class);
    private final List<ZmqPublisherWorker> workers = new ArrayList<>();
    private final ExecutorService executor = Executors.newCachedThreadPool();
    private final ConcurrentHashMap<String, ConcurrentHashMap<String, StockCode>> stockCache = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, ConcurrentHashMap<String, IndexInfo>> indexCache = new ConcurrentHashMap<>();
    private Integer count = 0;

    public MultiZmqPublisher(List<String> listServerAddress) {
        for (String address : listServerAddress) {
            ZmqPublisherWorker worker = new ZmqPublisherWorker(address);
            worker.setMultiZmqPublisher(this);
            workers.add(worker);
            executor.submit(worker);
        }
    }

    public void resendCacheToServer(ZmqPublisherWorker worker){
        logger.info("[MultiZmqPublisher] Resending cache to server...");
        try {
            for(Entry<String, ConcurrentHashMap<String, StockCode>> entry: stockCache.entrySet()){
                for(Map.Entry<String, StockCode> stockEntry: entry.getValue().entrySet()){
//                    Map<String, String> mapData = new HashMap<>();
//                    mapData = HelperUtils.convertToMap(stockEntry.getValue());
                    PublishEvent pubEvent = PublishEvent.builder()
                        .type("stock")
                        .data(stockEntry.getValue())
                        .build();
                    if( pubEvent.getType().contains("stock") && getPropertyValue(pubEvent.getData(), "stockCode")){
                        worker.send(pubEvent);
                    }
                }

            }
        } catch (Exception e) {
            logger.error("Error resending cache: {}", e.getMessage(), e);
        }
    }

    public void publish(Map<String, String> dataMessage)
        throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        String group = dataMessage.get("group");
        String message = dataMessage.get("message");
        String exchange = group.substring(1, 4);
        System.out.println("[MultiZmqPublisher] Publishing to exchange " + exchange);
        logger.info("----- [group] {} | message: {} -----", group, message);
        ExchangeMessageHandler handler = ExchangeHandlerFactory.getHandler(exchange);
         if (handler != null && message != null) {
             PublishEvent msgDiffPush = handler.handleMessage(exchange, message, stockCache, indexCache);
             // Kiểm tra stock
            if (msgDiffPush != null && msgDiffPush.getType().contains("stock") && getPropertyValue(msgDiffPush.getData(), "stockCode")) {
                for (ZmqPublisherWorker worker : workers) {
                    count++;
                    logger.info("[{}][group] {} | message: {}", group, message);
                    worker.send(msgDiffPush);
                }
            }
             if (msgDiffPush != null && msgDiffPush.getType().contains("index") && getPropertyValue(msgDiffPush.getData(), "exchange")) {
                 for (ZmqPublisherWorker worker : workers) {
                     count++;
                     logger.info("[{}][group] {} | message: {}", count, group, message);
                     worker.send(msgDiffPush);
                 }
             }
        }
    }
    
    public void shutdown() {
        for (ZmqPublisherWorker worker : workers) worker.shutdown();
        executor.shutdownNow();
    }
}