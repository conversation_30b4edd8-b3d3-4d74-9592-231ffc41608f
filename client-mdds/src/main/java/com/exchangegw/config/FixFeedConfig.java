package com.exchangegw.config;

import com.fasterxml.jackson.annotation.JsonProperty;

import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class FixFeedConfig {
    @JsonProperty("group")
    private String group;
    @JsonProperty("messageName")
    private String messageName;
    @JsonProperty("ip")
    private String ip;
    @JsonProperty("port")
    private int port;
    @JsonProperty("market")
    private String market;   

    @Override
    public String toString(){
        return "FixFeedConfig{" +
               "group='" + group + '\'' +
               ", messageName='" + messageName + '\'' +
               ", ip='" + ip + '\'' +
               ", port=" + port +
               ", market='" + market + '\'' +
               '}';
    }
}
