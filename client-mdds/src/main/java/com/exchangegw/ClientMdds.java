package com.exchangegw;

import com.exchangegw.core.FixWorker;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.net.DatagramPacket;
import java.net.InetAddress;
import java.net.MulticastSocket;
import java.net.SocketTimeoutException;
import java.util.HashMap;
import java.util.List;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import org.springframework.boot.autoconfigure.SpringBootApplication;

import com.exchangegw.config.FixFeedConfig;
import com.exchangegw.config.MddsConfig;

import com.exchangegw.publisher.MultiZmqPublisher;
import com.exchangegw.queue.MddsMessageQueue;
@SpringBootApplication
public class ClientMdds {
    private static final Logger logger = LoggerFactory.getLogger(MultiZmqPublisher.class);

    public static void main(String[] args){
        try {
            MddsMessageQueue messageQueue = new MddsMessageQueue();

            ObjectMapper mapper = new ObjectMapper();
            InputStream inputStream = ClientMdds.class.getClassLoader().getResourceAsStream("fix-mdds-config.json");
            if (inputStream == null) {
                throw new FileNotFoundException("Cannot find fix-mdds-config.json in resources");
            }

            MddsConfig config = mapper.readValue(inputStream, MddsConfig.class);
            String ipListen = config.getIpListen();
            List<FixFeedConfig> configs = config.getJoinGroupMessage();
            logger.info("[main] ipListen: {}", ipListen);
            logger.info("[main] Loaded: {}", configs.size());

            //Todo: Rào chổ này lại đọc file
            ExecutorService executor = Executors.newFixedThreadPool(configs.size());
            for(FixFeedConfig cfg: configs){
                executor.submit(new FixWorker(ipListen, cfg, messageQueue));
            }
            /// PROD
            List<String> servers = List.of(
                "tcp://************:9000",
                "tcp://***********:5555",
                "tcp://***********:9000",
                "tcp://***********:9000"
            );

            MultiZmqPublisher publisher = new MultiZmqPublisher(servers);
            // Thread lấy từ messageQueue và gửi sang publisher
            Thread dispatcher = new Thread(() -> {
                while (true) {
                    try {
                        HashMap<String, String> mapMessage = messageQueue.takeMessage();
                        publisher.publish(mapMessage);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            });
            dispatcher.setDaemon(true);
            dispatcher.start();
            Runtime.getRuntime().addShutdownHook(new Thread(publisher::shutdown));
            Thread.currentThread().join(); // block main thread
        } catch (Exception e) {
            logger.error("Failed to load configuration: {}", e.getMessage(), e);
        }
    }
}
