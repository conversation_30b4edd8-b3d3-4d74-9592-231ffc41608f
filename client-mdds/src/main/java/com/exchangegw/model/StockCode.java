package com.exchangegw.model;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StockCode implements Cloneable {
    private static final Logger logger = LoggerFactory.getLogger(StockCode.class);
    @JsonProperty("exchange")       // t207
    public String exchange;

    @JsonProperty("marketID")       //t30001
    public String marketID;

    @JsonProperty("stockCode") //  t30624 -> trả online
    public String stockCode;

    @JsonProperty("stockIsin") //  t55 -> chạy source
    public String stockIsin;

    @JsonProperty("ceiPrice")       // t1149
    public BigDecimal ceiPrice;
    @JsonProperty("floPrice")       // t1148
    public BigDecimal floPrice;
    @JsonProperty("refPrice")       // t20013
    public BigDecimal refPrice;

    @JsonProperty("purPrice3")
    public BigDecimal purPrice3;
    @JsonProperty("purVolume3")
    public Integer purVolume3;
    @JsonProperty("purPrice2")
    public BigDecimal purPrice2;
    @JsonProperty("purVolume2")
    public Integer purVolume2;
    @JsonProperty("purPrice1")
    public BigDecimal purPrice1;
    @JsonProperty("purVolume1")
    public Integer purVolume1;

    @JsonProperty("sellPrice1")
    public BigDecimal sellPrice1;
    @JsonProperty("sellVolume1")
    public Integer sellVolume1;
    @JsonProperty("sellPrice2")
    public BigDecimal sellPrice2;
    @JsonProperty("sellVolume2")
    public Integer sellVolume2;
    @JsonProperty("sellPrice3")
    public BigDecimal sellPrice3;
    @JsonProperty("sellVolume3")
    public Integer sellVolume3;


    @JsonProperty("totalMatchValue")
    public Float totalMatchValue;
    @JsonProperty("totalMatchVolume")
    public Integer totalMatchVolume;

    @JsonProperty("lowPrice")
    public BigDecimal lowPrice;
    @JsonProperty("hightPrice")
    public BigDecimal hightPrice;

    @JsonProperty("totalPurValueInvestor")
    public Float totalPurValueInvestor;
    @JsonProperty("totalPurVolumeInvestor")
    public Integer totalPurVolumeInvestor;
    @JsonProperty("totalSellValueInvestor")
    public Float totalSellValueInvestor;
    @JsonProperty("totalSellVolumeInvestor")
    public Integer totalSellVolumeInvestor;
    @JsonProperty("totalRoomInvestor")
    public Integer totalRoomInvestor;

    // Tổng room nước ngoài: T30557 - MF
    @JsonProperty("foreignerBuyPosblQty")
    public BigDecimal foreignerBuyPosblQty;

    // Tổng khối lượng giao dịch luỹ kế trong ngày: T387 - X
    @JsonProperty("totalVolumeTraded")
    public BigDecimal totalVolumeTraded;

    // Dữ liệu sở UTC
    @JsonProperty("sendingTime")
    private String sendingTime; //


    @JsonProperty("matchPrice")
    public BigDecimal matchPrice;
    @JsonProperty("matchPer")
    public Float matchPer;
    @JsonProperty("matchVolume")
    public BigDecimal matchVolume;

    @JsonProperty("side")
    private Integer side;

    @JsonProperty("averPrice")
    public Float averPrice;

    @JsonProperty("matchedOrders")
    @Builder.Default
    private List<MatchedOrder> matchedOrders =  new ArrayList<>();

    @Override
    public StockCode clone() {
        try {
            return (StockCode) super.clone();
        } catch (CloneNotSupportedException e) {
            throw new AssertionError();
        }
    }

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class MatchedOrder {
        private Integer volume;
        private Float price;
        private Integer size;
        private String time;
    }
    public void addMatchedOrders(Integer volume, Float price, Integer size, String time) {
        matchedOrders.add(new MatchedOrder(volume, price, size, time));
    }

    public void print() {
        logger.info("StockCode [exchange={}, stockCode={}, ceiPrice={}, floPrice={}, refPrice={}, " +
                "purPrice3={}, purVolume3={}, purPrice2={}, purVolume2={}, purPrice1={}, purVolume1={}, " +
                "matchPrice={}, matchPer={}, matchVolume={}, sellPrice1={}, sellVolume1={}, sellPrice2={}, " +
                "sellVolume2={}, sellPrice3={}, sellVolume3={}, totalMatchValue={}, totalMatchVolume={}, " +
                "averPrice={}, lowPrice={}, hightPrice={}, totalPurValueInvestor={}, totalPurVolumeInvestor={}, " +
                "totalSellValueInvestor={}, totalSellVolumeInvestor={}, totalRoomInvestor={}]",
                exchange, stockCode, ceiPrice, floPrice, refPrice,
                purPrice3, purVolume3, purPrice2, purVolume2, purPrice1, purVolume1,
                matchPrice, matchPer, matchVolume, sellPrice1, sellVolume1, sellPrice2,
                sellVolume2, sellPrice3, sellVolume3, totalMatchValue, totalMatchVolume,
                averPrice, lowPrice, hightPrice, totalPurValueInvestor, totalPurVolumeInvestor,
                totalSellValueInvestor, totalSellVolumeInvestor, totalRoomInvestor);
    }
}