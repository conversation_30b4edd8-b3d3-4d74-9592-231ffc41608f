package com.exchangegw.model;
import java.math.BigDecimal;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IndexInfo implements Cloneable {
    private String exchange; // _G1 / _G4
    private String marketID;          // T30001 ID xác định các thị trường STO|STX
    private String indexsTypeCode;    // T30167 Mã của chỉ số được tính và công bố trên thị trường
    private String indexsTypeName;           // T30632 Tên Tiếng Việt
    private String indexsTypeEngName;        // T30633 Tên Tiếng Anh
    private Float valueIndexes;       // T30217 Giá trị chỉ số được tính

    private BigDecimal totalVolumeTraded; // T381 Tổng khối lượng giao dịch
    private BigDecimal grossTradeAmt;     // T387 Tổng giá trị giao dịch

    private Integer fluctuationUpIssueCount;              // T30590 Số lượng cổ phiếu tăng
    private Integer fluctuationSteadinessIssueCount;      // T30591 Số lượng cổ phiếu không thay đổi
    private Integer fluctuationDownIssueCount;            // T30592 Số lượng cổ phiếu giảm
    private Integer fluctuationLowerLimitIssueCount;      // T30593 Số lượng cổ phiếu sàn
    private Integer fluctuationUpperLimitIssueCount;      // T30589 Số lượng cổ phiếu bằng giá trần

   // Dữ liệu sở UTC
    private String sendingTime; //

    @Override
    public IndexInfo clone() {
        try {
            return (IndexInfo) super.clone();
        } catch (CloneNotSupportedException e) {
            throw new RuntimeException("Clone not supported", e);
        }
    }
}